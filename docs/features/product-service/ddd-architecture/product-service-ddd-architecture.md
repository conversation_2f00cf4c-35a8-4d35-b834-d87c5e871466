# Product Service DDD架构设计总览

## 📋 架构概述

Product Service采用严格的Domain-Driven Design (DDD) 架构，实现"SKU为王"的库存型设计理念。本文档详细介绍DDD四层架构的设计原则、聚合边界管理、领域模型设计和业务规则实现。

## 🎯 核心设计理念

### SKU为王 (SKU as King)

> **核心原则**：所有业务操作都以SKU为最小操作单元，Product仅作为数据组织载体

#### 设计特点
- **业务中心化**：价格、库存、图片、供应商等所有业务属性都在SKU级别管理
- **查询优化**：条码扫描、库存查询等核心操作直接基于SKU设计，避免复杂JOIN
- **编辑自由度**：每个SKU可独立编辑所有业务属性，无需考虑SPU约束
- **类型隔离**：单规格、多规格、组合商品严格分离，避免复杂的类型转换逻辑

#### 与传统电商设计的区别

| 维度 | 传统电商设计 | SKU为王设计 |
|------|-------------|------------|
| **业务操作** | SPU+SKU双重操作 | 纯SKU操作 |
| **数据查询** | 需要JOIN两张表 | 单表查询 |
| **业务字段** | SPU和SKU都有业务字段 | 业务字段集中在SKU |
| **编辑复杂度** | 需要维护SPU-SKU一致性 | SKU独立编辑 |
| **存储效率** | 数据冗余较多 | 最小化冗余 |

## 🏗️ DDD四层架构设计

### 架构层次图
```
┌─────────────────────────────────────────────────────────────┐
│                    Interface Layer (接口层)                  │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │ ProductController│  │ProductQueryController│              │
│  │ (写操作)         │  │ (读操作)        │                   │
│  └─────────────────┘  └─────────────────┘                   │
├─────────────────────────────────────────────────────────────┤
│                  Application Layer (应用层)                  │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │ProductCommandService│ │ProductQueryService│              │
│  │ (命令服务)       │  │ (查询服务)       │                   │
│  └─────────────────┘  └─────────────────┘                   │
├─────────────────────────────────────────────────────────────┤
│                    Domain Layer (领域层)                     │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │  Product        │  │ProductDomainService│                │
│  │ (聚合根)         │  │ (领域服务)       │                   │
│  │  ├─ProductSku   │  └─────────────────┘                   │
│  │  └─ProductSkuCombo│                                      │
│  └─────────────────┘                                        │
├─────────────────────────────────────────────────────────────┤
│              Infrastructure Layer (基础设施层)                │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │ProductRepositoryImpl│ │ProductEntityConvertor│           │
│  │ (仓储实现)       │  │ (对象转换)       │                   │
│  │  ├─ProductMapper │  └─────────────────┘                   │
│  │  ├─ProductSkuMapper│                                     │
│  │  └─ProductSkuComboMapper│                                │
│  └─────────────────┘                                        │
└─────────────────────────────────────────────────────────────┘
```



## 🎯 总结

Product Service的DDD架构设计完美体现了"SKU为王"的库存型设计理念：

### 架构优势
1. **业务清晰**：聚合边界明确，业务规则集中在领域层
2. **性能优化**：批量验证、单表查询、缓存策略
3. **扩展性强**：新增商品类型只需扩展领域模型和服务
4. **维护性好**：严格分层，职责清晰，易于理解和修改

### 设计特色
1. **SKU中心化**：所有业务操作围绕SKU设计
2. **聚合完整性**：通过领域服务保证复杂业务规则
3. **事务边界**：聚合级别的强一致性保证
4. **并发安全**：乐观锁和版本控制

这种架构设计为复杂的商品管理业务提供了稳定、高效、可扩展的技术基础。