package com.ensign.web.product.product.application.convertor;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import com.ensign.web.product.product.application.pojo.cmd.CreateComboProductCmd;
import com.ensign.web.product.product.application.pojo.cmd.CreateMultiSpecProductCmd;
import com.ensign.web.product.product.application.pojo.cmd.CreateSingleSpecProductCmd;
import com.ensign.web.product.product.application.pojo.response.ProductDetailResponse;
import com.ensign.web.product.product.application.pojo.response.ProductResponse;
import com.ensign.web.product.product.domain.model.Product;
import com.ensign.web.product.product.domain.model.ProductSku;

/**
 * 商品应用层转换器
 * ：使用spring componentModel，标准命名（toDomain/toResponse）
 * 负责Command对象 → 领域对象的转换
 * 
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProductAppConvertor {

    // ===============================
    // Command → Domain 转换
    // ===============================

    /**
     * 单规格商品创建命令转领域对象
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "skus", ignore = true)
    @Mapping(target = "comboRelations", ignore = true)
    @Mapping(target = "productType", expression = "java(com.ensign.starter.common.enums.product.ProductTypeEnum.SINGLE_SPEC)")
    Product toDomain(CreateSingleSpecProductCmd cmd);

    /**
     * 多规格商品创建命令转领域对象
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "skus", ignore = true)
    @Mapping(target = "comboRelations", ignore = true)
    @Mapping(target = "productType", expression = "java(com.ensign.starter.common.enums.product.ProductTypeEnum.MULTI_SPEC)")
    Product toDomain(CreateMultiSpecProductCmd cmd);

    /**
     * 组合商品创建命令转领域对象
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "skus", ignore = true)
    @Mapping(target = "comboRelations", ignore = true)
    @Mapping(target = "productType", expression = "java(com.ensign.starter.common.enums.product.ProductTypeEnum.COMBO)")
    Product toDomain(CreateComboProductCmd cmd);

    /**
     * 单规格商品命令转SKU领域对象
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "productId", ignore = true)
    @Mapping(target = "skuCode", source = "productCode") // 单规格商品：productCode = skuCode
    @Mapping(target = "status", expression = "java(com.ensign.web.product.product.domain.enums.SkuStatus.ACTIVE)")
    @Mapping(target = "availableStockCache", source = "initialStock")
    @Mapping(target = "allocatedStockCache", constant = "0")
    @Mapping(target = "featured", constant = "false")
    @Mapping(target = "newArrival", constant = "false")
    ProductSku toSkuDomain(CreateSingleSpecProductCmd cmd);

    // ===============================
    // Domain → Response 转换
    // ===============================

    /**
     * 领域对象转响应对象
     */
    @Mapping(target = "skuCount", expression = "java(domain.getSkuCount())")
    ProductResponse toResponse(Product domain);

    /**
     * 领域对象转详细响应对象
     */
    @Mapping(target = "skuCount", expression = "java(domain.getSkuCount())")
    @Mapping(target = "skuList", source = "skus")
    @Mapping(target = "comboRelations", source = "comboRelations")
    ProductDetailResponse toDetailResponse(Product domain);

    /**
     * 领域对象列表转响应对象列表
     */
    List<ProductResponse> toResponseList(List<Product> domains);

    // ===============================
    // SKU相关转换
    // ===============================

    /**
     * SKU领域对象转响应对象
     */
    @Mapping(target = "stockStatus", expression = "java(domain.getStockStatus())")
    @Mapping(target = "canSell", expression = "java(domain.canSell())")
    @Mapping(target = "needsRestock", expression = "java(domain.needsRestock())")
    @Mapping(target = "profitMargin", expression = "java(domain.getProfitMargin())")
    com.ensign.web.product.product.application.pojo.response.ProductSkuResponse toSkuResponse(ProductSku domain);

    /**
     * SKU领域对象列表转响应对象列表
     */
    List<com.ensign.web.product.product.application.pojo.response.ProductSkuResponse> toSkuResponseList(List<ProductSku> domains);

    // ===============================
    // 辅助转换方法
    // ===============================

    /**
     * 多规格商品SKU信息转SKU领域对象
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "productId", ignore = true)
    @Mapping(target = "status", expression = "java(com.ensign.web.product.product.domain.enums.SkuStatus.ACTIVE)")
    @Mapping(target = "availableStockCache", source = "initialStock")
    @Mapping(target = "allocatedStockCache", constant = "0")
    @Mapping(target = "featured", constant = "false")
    @Mapping(target = "newArrival", constant = "false")
    ProductSku toSkuDomain(com.ensign.web.product.product.application.pojo.cmd.CreateMultiSpecProductCmd.MultiSpecSkuInfo skuInfo);

    /**
     * 多规格商品SKU信息列表转SKU领域对象列表
     */
    List<ProductSku> toSkuDomainList(List<com.ensign.web.product.product.application.pojo.cmd.CreateMultiSpecProductCmd.MultiSpecSkuInfo> skuInfoList);

    /**
     * 组合商品成分信息转组合关系领域对象
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "comboSkuId", ignore = true) // 在服务层设置
    @Mapping(target = "componentSkuId", source = "componentSkuId")
    @Mapping(target = "quantity", source = "quantity")
    com.ensign.web.product.product.domain.model.ProductSkuCombo toComboRelationDomain(
        com.ensign.web.product.product.application.pojo.cmd.CreateComboProductCmd.ComboComponentInfo componentInfo);

    /**
     * 组合商品成分信息列表转组合关系领域对象列表
     */
    List<com.ensign.web.product.product.domain.model.ProductSkuCombo> toComboRelationDomainList(
        List<com.ensign.web.product.product.application.pojo.cmd.CreateComboProductCmd.ComboComponentInfo> componentInfoList);
}
