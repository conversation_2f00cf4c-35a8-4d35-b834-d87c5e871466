package com.ensign.starter.web.servlet.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * 通用分页响应类
 *
 * @param <T> 分页内容的类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageResponse<T> {

    /**
     * 当前页码
     */
    private Long pageNum;

    /**
     * 每页大小
     */
    private Long pageSize;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 总页数
     */
    private int totalPages;

    /**
     * 当前页的数据
     */
    private List<T> items;

    /**
     * 下一页游标（可选）
     */
    private String nextCursor;

    /**
     * 上一页游标（可选）
     */
    private String previousCursor;

    /**
     * 静态方法：构造基于页码的分页响应
     *
     * @param pageNum  当前页码
     * @param pageSize 每页大小
     * @param total    总记录数
     * @param items    当前页的数据
     * @param <T>      数据类型
     * @return 分页响应
     */
    public static <T> PageResponse<T> of(Long pageNum, Long pageSize, Long total, List<T> items) {
        int totalPages = (int) Math.ceil((double) total / pageSize);
        return new PageResponse<>(pageNum, pageSize, total, totalPages, items, null, null);
    }

    /**
     * 静态方法：构造基于游标的分页响应（带页码信息）
     *
     * @param pageNum        当前页码
     * @param pageSize       每页大小
     * @param total          总记录数
     * @param items          当前页的数据
     * @param nextCursor     下一页游标
     * @param previousCursor 上一页游标
     * @param <T>            数据类型
     * @return 分页响应
     */
    public static <T> PageResponse<T> ofCursor(
            Long pageNum,
            Long pageSize,
            Long total,
            List<T> items,
            String nextCursor,
            String previousCursor
    ) {
        int totalPages = (int) Math.ceil((double) total / pageSize);
        return new PageResponse<>(pageNum, pageSize, total, totalPages, items, nextCursor, previousCursor);
    }

    public static <T> PageResponse<T> empty() {
        return new PageResponse<>(0L, 0L, 0L, 0, Collections.emptyList(), null, null);
    }

}
