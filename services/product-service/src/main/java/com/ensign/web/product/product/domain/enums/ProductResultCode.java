package com.ensign.web.product.product.domain.enums;

import com.ensign.starter.common.enums.ResultCode;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 产品领域特定错误码
 * 遵循CLAUDE.md规范：使用BizException + 领域特定ResultCode
 * 错误码命名规范：{DOMAIN}_{OBJECT}_{ACTION}_{REASON}
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ProductResultCode implements ResultCode {
    
    // ========== 商品相关错误 ==========
    
    /**
     * 商品编码已存在
     */
    PRODUCT_CODE_ALREADY_EXISTS(409, "PRODUCT_CODE_ALREADY_EXISTS", "商品编码已存在"),
    
    /**
     * 商品不存在
     */
    PRODUCT_NOT_FOUND(404, "PRODUCT_NOT_FOUND", "商品不存在"),
    
    /**
     * 商品编码格式不正确
     */
    PRODUCT_CODE_INVALID_FORMAT(422, "PRODUCT_CODE_INVALID_FORMAT", "商品编码格式不正确"),
    
    /**
     * 商品创建命令验证失败
     */
    PRODUCT_CREATE_COMMAND_INVALID(400, "PRODUCT_CREATE_COMMAND_INVALID", "商品创建命令验证失败"),
    
    /**
     * 商品状态不允许此操作
     */
    PRODUCT_STATUS_NOT_ALLOWED(400, "PRODUCT_STATUS_NOT_ALLOWED", "商品状态不允许此操作"),
    
    // ========== SKU相关错误 ==========
    
    /**
     * SKU编码已存在
     */
    SKU_CODE_ALREADY_EXISTS(409, "SKU_CODE_ALREADY_EXISTS", "SKU编码已存在"),
    
    /**
     * SKU不存在
     */
    SKU_NOT_FOUND(404, "SKU_NOT_FOUND", "SKU不存在"),
    
    /**
     * SKU编码格式不正确
     */
    SKU_CODE_INVALID_FORMAT(422, "SKU_CODE_INVALID_FORMAT", "SKU编码格式不正确"),
    
    /**
     * SKU库存不足
     */
    SKU_STOCK_INSUFFICIENT(400, "SKU_STOCK_INSUFFICIENT", "SKU库存不足"),
    
    /**
     * SKU状态不允许此操作
     */
    SKU_STATUS_NOT_ALLOWED(400, "SKU_STATUS_NOT_ALLOWED", "SKU状态不允许此操作"),
    
    /**
     * SKU价格信息无效
     */
    SKU_PRICE_INVALID(400, "SKU_PRICE_INVALID", "SKU价格信息无效"),
    
    // ========== 组合商品相关错误 ==========
    
    /**
     * 组合商品成分无效
     */
    COMBO_PRODUCT_COMPONENTS_INVALID(400, "COMBO_PRODUCT_COMPONENTS_INVALID", "组合商品成分无效"),
    
    /**
     * 组合商品成分SKU不存在
     */
    COMBO_PRODUCT_COMPONENT_SKU_NOT_FOUND(404, "COMBO_PRODUCT_COMPONENT_SKU_NOT_FOUND", "组合商品成分SKU不存在"),
    
    /**
     * 组合商品不能包含自身
     */
    COMBO_PRODUCT_CANNOT_CONTAIN_SELF(400, "COMBO_PRODUCT_CANNOT_CONTAIN_SELF", "组合商品不能包含自身"),
    
    // ========== 多租户相关错误 ==========
    
    /**
     * 业务伙伴ID不能为空
     */
    PARTNER_ID_REQUIRED(400, "PARTNER_ID_REQUIRED", "业务伙伴ID不能为空"),
    
    /**
     * 租户无权访问该资源
     */
    TENANT_ACCESS_DENIED(403, "TENANT_ACCESS_DENIED", "租户无权访问该资源"),
    
    /**
     * 业务伙伴无权访问该资源
     */
    PARTNER_ACCESS_DENIED(403, "PARTNER_ACCESS_DENIED", "业务伙伴无权访问该资源"),
    
    // ========== 分类和品牌相关错误 ==========
    
    /**
     * 商品分类不存在
     */
    PRODUCT_CATEGORY_NOT_FOUND(404, "PRODUCT_CATEGORY_NOT_FOUND", "商品分类不存在"),
    
    /**
     * 商品品牌不存在
     */
    PRODUCT_BRAND_NOT_FOUND(404, "PRODUCT_BRAND_NOT_FOUND", "商品品牌不存在"),
    
    // ========== 库存相关错误 ==========
    
    /**
     * 库存数量无效
     */
    STOCK_QUANTITY_INVALID(400, "STOCK_QUANTITY_INVALID", "库存数量无效"),
    
    /**
     * 库存操作类型无效
     */
    STOCK_OPERATION_TYPE_INVALID(400, "STOCK_OPERATION_TYPE_INVALID", "库存操作类型无效"),
    
    /**
     * 安全库存设置无效
     */
    SAFETY_STOCK_INVALID(400, "SAFETY_STOCK_INVALID", "安全库存设置无效"),
    
    // ========== 规格相关错误 ==========
    
    /**
     * 商品规格值无效
     */
    PRODUCT_SPEC_VALUES_INVALID(400, "PRODUCT_SPEC_VALUES_INVALID", "商品规格值无效"),
    
    /**
     * 单规格商品不能有多个SKU
     */
    SINGLE_SPEC_PRODUCT_MULTIPLE_SKUS(400, "SINGLE_SPEC_PRODUCT_MULTIPLE_SKUS", "单规格商品不能有多个SKU"),
    
    /**
     * 多规格商品必须有多个SKU
     */
    MULTI_SPEC_PRODUCT_SINGLE_SKU(400, "MULTI_SPEC_PRODUCT_SINGLE_SKU", "多规格商品必须有多个SKU"),

    /**
     * 未授权访问
     */
    UNAUTHORIZED_ACCESS(401, "UNAUTHORIZED_ACCESS", "未授权访问该资源");
    
    /**
     * HTTP状态码
     */
    private final Integer httpStatus;
    
    /**
     * 错误码（API传输值）
     */
    private final String errorCode;
    
    /**
     * 错误消息（中文显示）
     */
    private final String message;

    /**
     * 实现ResultCode接口的getHttpStatus方法
     */
    @Override
    public int getHttpStatus() {
        return this.httpStatus;
    }
}
