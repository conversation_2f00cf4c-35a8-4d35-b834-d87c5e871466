package com.ensign.web.product.product.application.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ensign.starter.common.exception.BizException;
import com.ensign.starter.util.security.AuthContextUtil;
import com.ensign.web.product.product.application.convertor.ProductAppConvertor;
import com.ensign.web.product.product.application.pojo.query.ProductPageQuery;
import com.ensign.web.product.product.application.pojo.response.ProductDetailResponse;
import com.ensign.web.product.product.application.pojo.response.ProductResponse;
import com.ensign.web.product.product.domain.enums.ProductResultCode;
import com.ensign.web.product.product.domain.model.Product;
import com.ensign.web.product.product.domain.repository.IProductRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 商品查询服务 负责处理商品相关的读操作（R） 遵循CQRS原则，分离命令和查询职责
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductQueryService {

  private final IProductRepository productRepository;
  private final ProductAppConvertor productAppConvertor;

  // ===============================
  // 商品分页查询
  // ===============================

  /**
   * 分页查询商品列表 ✅ ：不手动传递认证参数，由MyBatis Plus自动处理
   *
   * @param query 分页查询条件
   * @return 分页结果
   */
  public Page<ProductResponse> pageProducts(ProductPageQuery query) {
    log.debug("分页查询商品，页码：{}，页大小：{}", query.getPageNum(), query.getPageSize());

    // ✅ 调用仓储查询（认证上下文由MyBatis Plus自动处理）
    Page<Product> productPage = productRepository.pageProducts(query);

    // ✅ 使用转换器转换为响应对象
    return convertToProductResponsePage(productPage);
  }

  /**
   * 根据商品ID查询详情 ✅ ：不手动验证权限，由Repository层处理
   *
   * @param productId 商品ID
   * @return 商品详情
   */
  public ProductDetailResponse getProductDetail(Long productId) {
    log.debug("查询商品详情，商品ID：{}", productId);

    Product product = productRepository.findByIdWithSkus(productId);
    if (product == null) {
      return null;
    }

    // ✅ 使用转换器转换为详情响应对象
    return productAppConvertor.toDetailResponse(product);
  }

  /**
   * 根据商品编码查询详情 TODO: 待Repository接口更新后移除认证参数传递
   *
   * @param productCode 商品编码
   * @return 商品详情
   */
  public ProductDetailResponse getProductDetailByCode(String productCode) {
    log.debug("根据编码查询商品详情，商品编码：{}", productCode);

    // TODO: 暂时传递认证参数，待Repository接口更新
    // 根据访问类型获取认证信息
    Long tenantId = null;
    Long partnerId = null;

    try {
      if (AuthContextUtil.getAccessType() == com.ensign.starter.util.security.AccessType.TENANT) {
        var tenantUser = AuthContextUtil.getTenantUser();
        tenantId = tenantUser.getTenantId();
        partnerId = null; // 租户用户没有partnerId
      } else if (AuthContextUtil.getAccessType()
          == com.ensign.starter.util.security.AccessType.PARTNER_USER) {
        var businessPartnerUser = AuthContextUtil.getBusinessPartnerUser();
        tenantId = businessPartnerUser.getTenantId();
        partnerId = businessPartnerUser.getPartnerId();
      }
    } catch (Exception e) {
      log.warn("获取认证上下文失败: {}", e.getMessage());
      // 使用默认值或抛出异常
      throw new BizException(ProductResultCode.UNAUTHORIZED_ACCESS);
    }

    Product product = productRepository.findByProductCodeWithSkus(productCode, tenantId, partnerId);
    if (product == null) {
      return null;
    }

    // ✅ 使用转换器转换为详情响应对象
    return productAppConvertor.toDetailResponse(product);
  }

  // ===============================
  // 商品基础查询
  // ===============================

  /**
   * 根据商品ID查询基础信息 ✅ ：不手动验证权限，由Repository层处理
   *
   * @param productId 商品ID
   * @return 商品基础信息
   */
  public ProductResponse getProduct(Long productId) {
    log.debug("查询商品基础信息，商品ID：{}", productId);

    Product product = productRepository.findById(productId);
    if (product == null) {
      return null;
    }

    // ✅ 使用转换器转换为响应对象
    return productAppConvertor.toResponse(product);
  }

  /**
   * 根据商品编码查询基础信息 TODO: 待Repository接口更新后移除认证参数传递
   *
   * @param productCode 商品编码
   * @return 商品基础信息
   */
  public ProductResponse getProductByCode(String productCode) {
    log.debug("根据编码查询商品基础信息，商品编码：{}", productCode);

    // TODO: 暂时传递认证参数，待Repository接口更新
    // 根据访问类型获取认证信息
    Long tenantId = AuthContextUtil.getTenantId();
    Long partnerId = null;

    try {
      if (AuthContextUtil.isPartnerRequest()) {
        partnerId = AuthContextUtil.getPartnerId();
      }
    } catch (Exception e) {
      log.warn("获取认证上下文失败: {}", e.getMessage());
      // 使用默认值或抛出异常
      throw new BizException(ProductResultCode.UNAUTHORIZED_ACCESS);
    }

    Product product = productRepository.findByProductCode(productCode, tenantId, partnerId);
    if (product == null) {
      return null;
    }

    // ✅ 使用转换器转换为响应对象
    return productAppConvertor.toResponse(product);
  }

  /**
   * 检查商品编码是否存在
   *
   * @param productCode 商品编码
   * @return 是否存在
   */
  public boolean existsByProductCode(String productCode) {
    return productRepository.existsByProductCode(productCode);
  }

  /**
   * 检查SKU编码是否存在
   *
   * @param skuCode SKU编码
   * @return 是否存在
   */
  public boolean existsBySkuCode(String skuCode) {
    return productRepository.existsBySkuCode(skuCode);
  }

  // ===============================
  // 私有辅助方法
  // ===============================

  /** 转换为商品响应分页对象 ✅ 使用转换器进行对象转换 */
  private Page<ProductResponse> convertToProductResponsePage(Page<Product> productPage) {
    Page<ProductResponse> responsePage = new Page<>();
    responsePage.setCurrent(productPage.getCurrent());
    responsePage.setSize(productPage.getSize());
    responsePage.setTotal(productPage.getTotal());
    // ✅ 页数会自动计算，不需要手动设置

    // ✅ 使用转换器批量转换记录
    responsePage.setRecords(productAppConvertor.toResponseList(productPage.getRecords()));

    return responsePage;
  }
}
