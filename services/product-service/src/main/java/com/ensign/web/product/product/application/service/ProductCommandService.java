package com.ensign.web.product.product.application.service;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ensign.starter.common.exception.BizException;
import com.ensign.web.product.product.application.convertor.ProductAppConvertor;
import com.ensign.web.product.product.application.pojo.cmd.CreateComboProductCmd;
import com.ensign.web.product.product.application.pojo.cmd.CreateMultiSpecProductCmd;
import com.ensign.web.product.product.application.pojo.cmd.CreateSingleSpecProductCmd;
import com.ensign.web.product.product.domain.enums.ProductResultCode;
import com.ensign.web.product.product.domain.enums.SkuStatus;
import com.ensign.web.product.product.domain.model.Product;
import com.ensign.web.product.product.domain.model.ProductSku;
import com.ensign.web.product.product.domain.model.ProductSkuCombo;
import com.ensign.web.product.product.domain.service.ProductDomainService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 商品命令服务
 * 负责处理商品相关的写操作（CUD）
 * 遵循CQRS原则，分离命令和查询职责
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductCommandService {

    private final ProductDomainService productDomainService;
    private final ProductAppConvertor productAppConvertor;

    // ===============================
    // 单规格商品创建
    // ===============================

    /**
     * 创建单规格商品
     *
     * @param cmd 创建单规格商品命令
     * @return 创建的商品ID
     */
    @Transactional
    public Long createSingleSpecProduct(CreateSingleSpecProductCmd cmd) {
        log.info("开始创建单规格商品，商品编码：{}", cmd.getProductCode());

        // 1. 命令验证
        validateSingleSpecProductCmd(cmd);

        // 2. 使用转换器将命令转换为领域对象
        Product product = productAppConvertor.toDomain(cmd);
        ProductSku sku = productAppConvertor.toSkuDomain(cmd);

        // 3. 设置SKU详细信息
        updateSkuFromSingleSpecCmd(sku, cmd);

        // 4. 将SKU添加到商品聚合
        product.addSku(sku);

        // 5. 调用领域服务创建商品（使用转换后的领域对象）
        Product savedProduct = productDomainService.createSingleSpecProductFromDomain(product);

        log.info("单规格商品创建成功，商品ID：{}，SKU编码：{}",
                savedProduct.getId(), savedProduct.getSkus().getFirst().getSkuCode());
        return savedProduct.getId();
    }

    /**
     * 创建多规格商品
     *
     * @param cmd 创建多规格商品命令
     * @return 创建的商品ID
     */
    @Transactional
    public Long createMultiSpecProduct(CreateMultiSpecProductCmd cmd) {
        log.info("开始创建多规格商品，商品编码：{}，SKU数量：{}",
                cmd.getProductCode(), cmd.getSkuList().size());

        // 1. 命令验证
        validateMultiSpecProductCmd(cmd);

        // 2. 使用转换器构建商品和SKU列表（不手动传递认证信息）
        Product product = productAppConvertor.toDomain(cmd);
        List<ProductSku> skuList = productAppConvertor.toSkuDomainList(cmd.getSkuList());

        // 3. 设置SKU详细信息
        for (int i = 0; i < skuList.size(); i++) {
            updateSkuFromMultiSpecCmd(skuList.get(i), cmd.getSkuList().get(i));
        }

        // 4. 将SKU列表添加到商品聚合
        product.addSkus(skuList);

        // 5. 调用领域服务创建商品（使用转换后的领域对象）
        Product savedProduct = productDomainService.createMultiSpecProductFromDomain(product);

        log.info("多规格商品创建成功，商品ID：{}，SKU数量：{}",
                savedProduct.getId(), savedProduct.getSkuCount());
        return savedProduct.getId();
    }

    /**
     * 创建组合商品
     *
     * @param cmd 创建组合商品命令
     * @return 创建的商品ID
     */
    @Transactional
    public Long createComboProduct(CreateComboProductCmd cmd) {
        log.info("开始创建组合商品，商品编码：{}，成分数量：{}",
                cmd.getProductCode(), cmd.getComponents().size());

        // 1. 命令验证
        validateComboProductCmd(cmd);

        // 2. 使用转换器构建组合SKU（不手动传递认证信息）
        ProductSku comboSku = buildComboSkuFromCmd(cmd);

        // 3. 使用转换器构建组合关系列表（不手动传递认证信息）
        List<ProductSkuCombo> comboRelations = productAppConvertor.toComboRelationDomainList(cmd.getComponents());

        // 4. 调用领域服务创建组合商品（不手动传递认证信息）
        Product product = productDomainService.createComboProduct(
                cmd.getProductCode(),
                comboSku,
                comboRelations,
                cmd.getCategoryId()
        );

        // ✅ 移除手动设置租户和伙伴信息，由MyBatis Plus自动填充

        log.info("组合商品创建成功，商品ID：{}，成分数量：{}",
                product.getId(), product.getComboRelations().size());
        return product.getId();
    }

    // ===============================
    // 多规格商品创建
    // ===============================

    /**
     * 验证单规格商品创建命令
     */
    private void validateSingleSpecProductCmd(CreateSingleSpecProductCmd cmd) {
        if (!cmd.isValid()) {
            throw new BizException(ProductResultCode.PRODUCT_CREATE_COMMAND_INVALID);
        }
    }

    /**
     * 从单规格命令更新SKU信息
     */
    private void updateSkuFromSingleSpecCmd(ProductSku sku, CreateSingleSpecProductCmd cmd) {

        // 设置价格信息
        if (cmd.getPrice() != null) {
            sku.updatePriceInfo(cmd.getPrice(), cmd.getCost(), cmd.getMarketPrice());
        }

        // 设置初始库存信息
        if (cmd.getInitialStock() != null) {
            sku.setInitialStock(cmd.getInitialStock(), cmd.getSafetyStock());
        }

        // 设置物理属性
        if (cmd.getNetWeight() != null || cmd.getLength() != null) {
            sku.updatePhysicalInfo(cmd.getNetWeight(), cmd.getGrossWeight(),
                    cmd.getLength(), cmd.getWidth(), cmd.getHeight());
        }

        // 设置展示信息
        if (cmd.getMainImage() != null) {
            sku.updateDisplayInfo(cmd.getMainImage(), null, cmd.getVideoLink());
        }

        // 设置营销属性
        if (cmd.getFeatured() != null) {
            sku.setFeatured(cmd.getFeatured());
        }
        if (cmd.getNewArrival() != null) {
            sku.setNewArrival(cmd.getNewArrival());
        }

        // 设置配送信息
        sku.setShippingTemplateId(cmd.getShippingTemplateId());
        sku.setFreeShippingThreshold(cmd.getFreeShippingThreshold());
    }

    /**
     * 从多规格命令更新SKU信息
     */
    private void updateSkuFromMultiSpecCmd(ProductSku sku, CreateMultiSpecProductCmd.MultiSpecSkuInfo skuInfo) {

        // 设置价格信息
        if (skuInfo.getPrice() != null) {
            sku.updatePriceInfo(skuInfo.getPrice(), skuInfo.getCost(), skuInfo.getMarketPrice());
        }

        // 设置初始库存信息
        if (skuInfo.getInitialStock() != null) {
            sku.setInitialStock(skuInfo.getInitialStock(), skuInfo.getSafetyStock());
        }

        // 设置物理属性
        if (skuInfo.getNetWeight() != null || skuInfo.getLength() != null) {
            sku.updatePhysicalInfo(skuInfo.getNetWeight(), skuInfo.getGrossWeight(),
                    skuInfo.getLength(), skuInfo.getWidth(), skuInfo.getHeight());
        }

        // 设置展示信息
        if (skuInfo.getMainImage() != null) {
            sku.updateDisplayInfo(skuInfo.getMainImage(), null, skuInfo.getVideoLink());
        }

        // 设置营销属性
        if (skuInfo.getFeatured() != null) {
            sku.setFeatured(skuInfo.getFeatured());
        }
        if (skuInfo.getNewArrival() != null) {
            sku.setNewArrival(skuInfo.getNewArrival());
        }
    }

    /**
     * 验证多规格商品创建命令
     */
    private void validateMultiSpecProductCmd(CreateMultiSpecProductCmd cmd) {
        if (!cmd.isValid()) {
            throw new BizException(ProductResultCode.PRODUCT_CREATE_COMMAND_INVALID);
        }
    }

    // ✅ 移除buildSkuListFromCmd和buildSkuFromMultiSpecInfo方法
    // 现在使用ProductAppConvertor.toSkuDomainList()进行转换

    /**
     * 验证组合商品创建命令
     */
    private void validateComboProductCmd(CreateComboProductCmd cmd) {
        if (!cmd.isValid()) {
            throw new BizException(ProductResultCode.PRODUCT_CREATE_COMMAND_INVALID);
        }
    }

    /**
     * 从命令构建组合SKU（不传递认证参数，由MyBatis Plus自动填充）
     */
    private ProductSku buildComboSkuFromCmd(CreateComboProductCmd cmd) {
        CreateComboProductCmd.ComboSkuInfo skuInfo = cmd.getComboSku();

        ProductSku comboSku = ProductSku.builder()
                .skuCode(skuInfo.getSkuCode())
                .skuName(skuInfo.getSkuName())
                .status(SkuStatus.ACTIVE)
                // ✅ 移除手动设置的审计字段，由MyBatis Plus自动填充
                .build();

        // 设置价格信息
        if (skuInfo.getPrice() != null) {
            comboSku.updatePriceInfo(skuInfo.getPrice(), skuInfo.getCost(), skuInfo.getMarketPrice());
        }

        // 设置库存信息
        if (skuInfo.getInitialStock() != null) {
            comboSku.updateStockInfo(skuInfo.getInitialStock(), 0, skuInfo.getSafetyStock());
        }

        // 设置展示信息
        if (skuInfo.getMainImage() != null) {
            comboSku.updateDisplayInfo(skuInfo.getMainImage(), null, skuInfo.getVideoLink());
        }

        // 设置营销属性
        if (skuInfo.getFeatured() != null) {
            comboSku.setFeatured(skuInfo.getFeatured());
        }
        if (skuInfo.getNewArrival() != null) {
            comboSku.setNewArrival(skuInfo.getNewArrival());
        }

        return comboSku;
    }

    // ✅ 移除buildComboRelationsFromCmd和buildComboRelationFromInfo方法
    // 现在使用ProductAppConvertor.toComboRelationDomainList()进行转换
}