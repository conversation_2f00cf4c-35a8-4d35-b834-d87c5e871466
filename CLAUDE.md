# CLAUDE.md - Ensign Platform Memory

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🎯 项目概览

**Ensign Platform** 是基于领域驱动设计（DDD）原则构建的统一微服务物流货运管理平台。该平台为货运代理商提供全面的多租户功能，包括认证、计费、运输和客户关系管理。

**架构**: Spring Boot 3.x 后端 + React 19 前端，采用微服务架构，包含API网关和服务发现。

## 🔧 MCP工具使用指南

### **🎯 核心MCP工具配置**
本项目集成了多个MCP（模型上下文协议）服务器，提供强大的开发辅助功能：

- **Sequential Thinking** 🧠：用于复杂问题分析和多步骤解决方案（优先使用于所有复杂分析场景）
- **Context7** 📚：获取最新的库文档和项目规则
- **MySQL** 🗄️：数据库操作和优化（使用 `mysql_query_mysql` 等工具）
- **Filesystem** 📁：高级文件操作
- **Playwright** 🌐：浏览器自动化和测试
- **Github** 🐙：Git仓库管理和协作

### **⚠️ MCP工具使用原则**
```yaml
优先级规则:
  🧠 Sequential Thinking: 所有复杂分析、多步骤规划、架构设计
  🗄️ MySQL工具: 数据库查询、表结构分析、性能优化
  📚 Context7: 获取库文档前必须先resolve-library-id
  📁 Filesystem: 批量文件操作、目录结构分析
  🌐 Playwright: UI测试、浏览器自动化

使用场景:
  ✅ 复杂业务逻辑分析 → Sequential Thinking
  ✅ 数据库设计和查询 → MySQL工具
  ✅ 库文档查询 → Context7 (先resolve-library-id)
  ✅ 文件批量处理 → Filesystem工具
  ❌ 简单问题不需要工具链
```

# 🏗️ 后端开发指南

## 🎯 核心架构原则

### **DDD四层架构设计**
```java
// ✅ 严格分层依赖关系
interfaces → application → domain ← infrastructure

// 🎯 聚合根识别原则
- 领域核心聚合根（承载主要业务逻辑和状态变更）
- 轻量级数据容器（主要用于数据展示和基础信息管理）
- 聚合边界: 基于业务逻辑复杂性和状态管理独立性定义
- 一个聚合一个仓储: 确保逻辑内聚性，避免过度设计
```

### **CQRS命令查询分离**
```java
// ✅ 命令服务：处理写操作
@Service
@Transactional(readOnly = true)
public class TenantRoleCommandService {
    @Transactional(rollbackFor = Exception.class)
    public TenantRole createRole(CreateTenantRoleCmd cmd) { }
}

// ✅ 查询服务：处理读操作
@Service
@Transactional(readOnly = true)
public class TenantRoleQueryService {
    public PageResponse<TenantRole> pageQuery(TenantRoleQuery query) { }
}
```

### **多租户数据隔离**
```java
// ✅ 自动租户过滤：依赖MyBatis Plus自动填充
// ❌ 禁止手动传递tenant_id参数
@Data
public class CreateTenantRoleCmd {
    // ❌ 移除tenantId字段，由自动填充机制处理
    private String roleCode;
    private String roleName;
}
```

## 🏗️ 技术栈标准

### **核心技术组件**
- **Java 21** + Spring Boot 3.5.3
- **Spring Cloud 2025.0.0** + Alibaba组件
- **MyBatis Plus 3.5.12** + MySQL 9.3.0
- **Sa-Token 1.44.0** + Redis/Redisson
- **MapStruct 1.6.3** + RocketMQ 2.3.3

### **必需Starter包**
```gradle
dependencies {
    // DDD核心包
    implementation project(':starters:ensign-spring-boot-starter-common')
    implementation project(':starters:ensign-spring-boot-starter-web')
    implementation project(':starters:ensign-spring-boot-starter-data-mysql')
    implementation project(':starters:ensign-spring-boot-starter-auth')
}
```

## 🔧 实现模式与最佳实践

### **🗄️ 实体继承架构体系**
```java
// ✅ 完整继承层次
AbstractBaseEntity (抽象基类)
├── MinimalEntity (最小实体 - 系统配置)
├── SimpleEntity (简单实体 - 关联表)
├── TenantScopedEntity (租户范围实体 - 租户业务)
│   ├── OrganizationScopedEntity (组织范围实体)
│   │   └── OrganizationPartnerScopedEntity (组织业务伙伴范围)
│   └── PartnerScopedEntity (业务伙伴范围 - 跨租户)
└── SystemScopedEntity (系统范围实体 - 租户主体)

// ✅ 基类选择示例
@TableName("tenant_role")
public class TenantRoleEntity extends TenantScopedEntity {
    // 自动继承：tenant_id + 完整审计字段
}

@TableName("product")
public class ProductEntity extends PartnerScopedEntity {
    // 自动继承：tenant_id + partner_id + 审计字段
}
```

### **🔄 MapStruct转换器规范**
```java
// ✅ 应用层转换器：{Entity}AppConvertor
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TenantRoleAppConvertor {
    TenantRoleAppConvertor INSTANCE = Mappers.getMapper(TenantRoleAppConvertor.class);

    // Command/Query -> Domain
    @Mapping(target = "tenantId", expression = "java(getCurrentTenantId())")
    @Mapping(target = "id", ignore = true)
    TenantRole toDomain(CreateTenantRoleCmd cmd);

    // Domain -> Response
    TenantRoleResponse toResponse(TenantRole domain);
    TenantRoleDetailResponse toDetailResponse(TenantRole domain);
}

// ✅ 实体转换器：{Entity}EntityConvertor
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TenantRoleEntityConvertor {
    TenantRole toDomain(TenantRoleEntity entity);
    TenantRoleEntity toEntity(TenantRole domain);
}
```

### **🏪 仓储模式实现**
```java
// ✅ 简化仓储接口（领域层）
public interface ITenantRoleRepository {
    TenantRole save(TenantRole role);
    TenantRole getById(Long id);
    PageResponse<TenantRole> pageQuery(TenantRoleQuery query);
    List<TenantRole> listByTenantId(Long tenantId);
    boolean existsByRoleCode(String roleCode);
}

// ✅ 仓储实现（基础设施层）
@Repository
@RequiredArgsConstructor
public class TenantRoleRepositoryImpl implements ITenantRoleRepository {
    private final TenantRoleMapper roleMapper;
    private final TenantRoleEntityConvertor entityConvertor;

    @Override
    public PageResponse<TenantRole> pageQuery(TenantRoleQuery query) {
        LambdaQueryWrapper<TenantRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TenantRoleEntity::getTenantId, TenantContextHolder.getTenantId())
               .like(StringUtils.hasText(query.getRoleName()),
                     TenantRoleEntity::getRoleName, query.getRoleName());
        // MyBatis Plus自动处理分页和转换
    }
}
```

### **🔑 多租户上下文处理实现**
```java
// ✅ 业务伙伴自服务上下文处理
@Service
@RequiredArgsConstructor
public class ProductService {

    // ✅ 业务伙伴处理自己的产品，不显式传递partner_id
    public Product createProduct(CreateProductCmd cmd) {
        // AuthContextUtil自动处理partner_id和tenant_id
        BusinessPartnerUser partnerUser = AuthContextUtil.getBusinessPartnerUser();

        Product product = Product.builder()
            .productCode(cmd.getProductCode())
            .productName(cmd.getProductName())
            // ❌ 不要手动设置：.partnerId(partnerUser.getPartnerId())
            // ❌ 不要手动设置：.tenantId(partnerUser.getTenantId())
            .build();

        return productRepository.save(product); // 自动填充partner_id和tenant_id
    }
}

// ✅ 租户用户访问业务伙伴数据
@Service
@RequiredArgsConstructor
public class TenantProductService {

    // ✅ 租户用户查询特定业务伙伴的产品，必须包含partner_id
    public PageResponse<Product> queryPartnerProducts(PartnerProductQuery query) {
        TenantAuthUser tenantUser = AuthContextUtil.getTenantUser();

        // ✅ 必须在查询中包含partner_id
        if (query.getPartnerId() == null) {
            throw new BizException(StandardResultCode.PARAMETER_MISSING, "partner_id");
        }

        // ❌ 不要手动设置tenant_id，由AuthContextUtil自动处理
        return productRepository.pageQueryByPartner(query);
    }
}

// ✅ AuthContextUtil使用示例
@Component
public class ContextHelper {

    public void demonstrateContextUsage() {
        // 判断当前用户类型
        if (AuthContextUtil.isTenantUser()) {
            TenantAuthUser tenantUser = AuthContextUtil.getTenantUser();
            // 租户用户逻辑
        } else if (AuthContextUtil.isClientUser()) {
            ClientUser clientUser = AuthContextUtil.getClientUser();
            // 客户用户逻辑
        }

        // 获取业务伙伴用户
        try {
            BusinessPartnerUser partnerUser = AuthContextUtil.getBusinessPartnerUser();
            // 业务伙伴用户逻辑
        } catch (IllegalStateException e) {
            // 当前不是业务伙伴用户上下文
        }
    }
}
```

## 🗄️ 实体继承与数据库设计

### **🎯 实体继承架构体系**
```java
AbstractBaseEntity (抽象基类)
├── MinimalEntity (最小实体 - 系统配置)
├── SimpleEntity (简单实体 - 关联表)
├── TenantScopedEntity (租户范围实体 - 租户业务)
│   ├── OrganizationScopedEntity (组织范围实体 - 组织业务)
│   │   └── OrganizationPartnerScopedEntity (组织业务伙伴范围)
│   └── PartnerScopedEntity (业务伙伴范围 - 跨租户)
└── SystemScopedEntity (系统范围实体 - 租户主体)
```

### **✅ 基类选择标准**
```java
// ✅ 租户业务实体
@TableName("tenant_role")
public class TenantRoleEntity extends TenantScopedEntity {
    // 自动继承：tenant_id + 完整审计字段
}

// ✅ 业务伙伴实体（跨租户）
@TableName("product")
public class ProductEntity extends PartnerScopedEntity {
    // 自动继承：tenant_id + partner_id + 审计字段
}

// ✅ 系统配置实体
@TableName("system_dictionary")
public class SystemDictionaryEntity extends MinimalEntity {
    // 仅基础字段：id + created_at + updated_at
}
```

### **🔧 审计字段自动填充**
```java
// ✅ 企业级审计字段（自动填充）
- created_at, updated_at (时间审计)
- created_by, updated_by (用户审计)
- created_by_type, updated_by_type (用户类型)
- operation_ip, user_agent (操作审计)
- version (乐观锁版本控制)
- is_deleted (逻辑删除)
```

## 🔄 MapStruct转换器模式

### **🎯 转换器命名规范**
```java
// ✅ 应用层转换器：{Entity}AppConvertor
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TenantRoleAppConvertor {
    TenantRoleAppConvertor INSTANCE = Mappers.getMapper(TenantRoleAppConvertor.class);

    // Command/Query -> Domain
    TenantRole toDomain(CreateTenantRoleCmd cmd);
    TenantRole toDomain(UpdateTenantRoleCmd cmd);

    // Domain -> Response
    TenantRoleResponse toResponse(TenantRole domain);
    TenantRoleDetailResponse toDetailResponse(TenantRole domain);
}

// ✅ 实体转换器：{Entity}EntityConvertor
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TenantRoleEntityConvertor {
    // Entity <-> Domain 双向转换
    TenantRole toDomain(TenantRoleEntity entity);
    TenantRoleEntity toEntity(TenantRole domain);
}

// ✅ 远程调用转换器：{Service}RemoteConvertor
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserRemoteConvertor {
    // Domain -> Remote Request
    GetUserRequest toRemoteRequest(Long userId);
    // Remote Response -> Domain
    User toDomain(GetUserResponse response);
}
```

### **🔧 复杂映射配置**
```java
// ✅ 字段映射和条件转换
@Mapping(target = "roleCode", source = "code")
@Mapping(target = "status", source = "active", qualifiedByName = "booleanToStatus")
@Mapping(target = "tenantId", expression = "java(getCurrentTenantId())")
@Mapping(target = "id", ignore = true)
TenantRole toDomain(CreateTenantRoleCmd cmd);

// ✅ 自定义转换方法
@Named("booleanToStatus")
default StatusEnum booleanToStatus(Boolean active) {
    return active != null && active ? StatusEnum.ACTIVE : StatusEnum.INACTIVE;
}
```

## ⚠️ 错误处理与异常规范

### **🎯 统一异常体系**
```java
// ✅ 业务异常：使用BizException + 领域特定ResultCode
@ErrorCodeEnum("role")
public enum RoleResultCode implements ResultCode {
    ROLE_CODE_EXISTS(409, "ROLE_CODE_EXISTS", "角色编码已存在"),
    ROLE_NOT_FOUND(404, "ROLE_NOT_FOUND", "角色不存在"),
    ROLE_CODE_INVALID_FORMAT(422, "ROLE_CODE_INVALID_FORMAT", "角色编码格式不正确");
}

// ✅ 抛出业务异常
if (roleRepository.existsByRoleCode(cmd.getRoleCode())) {
    throw new BizException(RoleResultCode.ROLE_CODE_EXISTS, cmd.getRoleCode());
}

// ❌ 禁止：直接使用原生异常
throw new IllegalArgumentException("参数错误");  // 绝对禁止
throw new RuntimeException("业务处理失败");      // 绝对禁止
```

### **📋 错误码命名规范**
```java
// ✅ 语义化命名：{DOMAIN}_{OBJECT}_{ACTION}_{REASON}
FREIGHT_CHARGE_CODE_NOT_FOUND(404, "FREIGHT_CHARGE_CODE_NOT_FOUND", "费用代码不存在")
USER_ACCOUNT_CREATE_FAILED(400, "USER_ACCOUNT_CREATE_FAILED", "用户账户创建失败")
ORDER_SHIPMENT_CANCEL_DENIED(403, "ORDER_SHIPMENT_CANCEL_DENIED", "订单发货取消被拒绝")

// ✅ errorCode必须与枚举常量名称完全一致
// ✅ 使用统一的UPPER_SNAKE_CASE命名风格
// ✅ HTTP状态码选择正确（400/401/403/404/409/422/500）
```

## 🏪 仓储模式与MyBatis Plus实现

### **🎯 仓储接口设计**
```java
// ✅ 领域层仓储接口（简化设计）
public interface ITenantRoleRepository {
    TenantRole save(TenantRole role);                    // 保存
    TenantRole getById(Long id);                         // 详情查询
    PageResponse<TenantRole> pageQuery(TenantRoleQuery query); // 分页查询
    List<TenantRole> listByTenantId(Long tenantId);      // 列表查询
    boolean existsByRoleCode(String roleCode);           // 存在性检查
    void deleteById(Long id);                            // 删除
}

// ❌ 禁止：过度设计的仓储接口
// 避免包含大量查询方法，保持接口简洁
```

### **🔧 MyBatis Plus最佳实践**
```java
// ✅ 仓储实现（基础设施层）
@Repository
@RequiredArgsConstructor
public class TenantRoleRepositoryImpl implements ITenantRoleRepository {

    private final TenantRoleMapper roleMapper;
    private final TenantRoleEntityConvertor entityConvertor;

    @Override
    public TenantRole save(TenantRole role) {
        TenantRoleEntity entity = entityConvertor.toEntity(role);

        if (entity.getId() == null) {
            roleMapper.insert(entity);  // MyBatis Plus自动填充审计字段
        } else {
            roleMapper.updateById(entity);
        }

        return entityConvertor.toDomain(entity);
    }

    @Override
    public PageResponse<TenantRole> pageQuery(TenantRoleQuery query) {
        LambdaQueryWrapper<TenantRoleEntity> wrapper = new LambdaQueryWrapper<>();
        // ✅ 自动租户过滤（MyBatis Plus插件处理）
        wrapper.eq(TenantRoleEntity::getTenantId, TenantContextHolder.getTenantId())
               .like(StringUtils.hasText(query.getRoleName()),
                     TenantRoleEntity::getRoleName, query.getRoleName())
               .orderByDesc(TenantRoleEntity::getCreatedAt);

        Page<TenantRoleEntity> page = new Page<>(query.getPageNum(), query.getPageSize());
        Page<TenantRoleEntity> result = roleMapper.selectPage(page, wrapper);

        List<TenantRole> roles = result.getRecords().stream()
                .map(entityConvertor::toDomain)
                .collect(Collectors.toList());

        return PageResponse.of(query.getPageNum(), query.getPageSize(),
                              result.getTotal(), roles);
    }
}
```

### **⚠️ 多租户自动处理**
```java
// ✅ 依赖MyBatis Plus自动填充，不手动传递tenant_id
@Data
public class CreateTenantRoleCmd {
    // ❌ 移除tenantId字段，由自动填充机制处理
    private String roleCode;
    private String roleName;
    // ... 其他业务字段
}

// ✅ 服务层不需要手动设置tenant_id
@Service
public class TenantRoleCommandService {
    public TenantRole createRole(CreateTenantRoleCmd cmd) {
        // ✅ tenant_id将由MyBatis Plus自动填充
        TenantRole role = TenantRole.builder()
            .roleCode(cmd.getRoleCode())
            .roleName(cmd.getRoleName())
            .build();

        return roleRepository.save(role);
    }
}
```

## ⚠️ 关键禁忌事项

### **🚫 后端开发禁忌**
```java
// ❌ 绝对禁止：使用原生异常
throw new IllegalArgumentException("参数错误");  // 绝对禁止
throw new RuntimeException("业务处理失败");      // 绝对禁止

// ✅ 正确做法：使用BizException + ResultCode
if (roleRepository.existsByRoleCode(cmd.getRoleCode())) {
    throw new BizException(RoleResultCode.ROLE_CODE_EXISTS, cmd.getRoleCode());
}

// ❌ 禁止：控制器直接调用Repository
@RestController
public class BadController {
    @Autowired
    private ITenantRoleRepository roleRepository;  // 错误：跳过应用层
}

// ❌ 禁止：手动传递tenant_id
@Data
public class CreateTenantRoleCmd {
    private Long tenantId;  // 错误：应由自动填充处理
}

// ❌ 禁止：布尔字段使用'is'前缀
private Boolean isActive;   // 错误：导致RPC解析错误
private Boolean active;     // 正确：避免'is'前缀
```

### **🔒 数据库设计禁忌**
```sql
-- ❌ 禁止：使用float和double
price FLOAT;              -- 错误：精度问题
amount DOUBLE;            -- 错误：精度问题

-- ✅ 正确：使用decimal
price DECIMAL(10,2);      -- 正确：精确小数

-- ❌ 禁止：缺少必备字段
CREATE TABLE bad_table (
    name VARCHAR(100)     -- 错误：缺少id、审计字段
);
```

## 📋 后端开发检查清单

### **🏗️ DDD架构检查**
```yaml
聚合设计:
  ✅ ProductSku作为产品领域核心聚合根
  ✅ 聚合边界基于业务逻辑复杂性定义
  ✅ 一个聚合一个仓储接口
  ✅ 聚合根封装业务规则和状态变更

分层架构:
  ✅ 严格四层架构分离
  ✅ 接口层仅处理HTTP请求响应
  ✅ 应用层协调业务流程
  ✅ 领域层包含核心业务逻辑
  ✅ 基础设施层处理技术实现

CQRS模式:
  ✅ 命令服务处理写操作
  ✅ 查询服务处理读操作
  ✅ 控制器分别注入两种服务
  ✅ 事务边界正确设置
```

### **🔧 实现质量检查**
```yaml
实体继承:
  ✅ 根据业务特性选择合适基类
  ✅ 审计字段自动填充配置
  ✅ 多租户数据隔离
  ✅ 布尔字段避免'is'前缀

转换器检查:
  ✅ MapStruct标准配置
  ✅ 命名规范符合约定
  ✅ 复杂映射使用@Mapping注解
  ✅ 错误处理和空值检查

仓储模式:
  ✅ 简化仓储接口设计
  ✅ MyBatis Plus自动填充
  ✅ LambdaQueryWrapper查询构建
  ✅ 分页响应统一格式

异常处理:
  ✅ 使用BizException + ResultCode
  ✅ 错误码语义化命名
  ✅ HTTP状态码正确选择
  ✅ 全局异常处理配置
```

## 📁 命名规范与项目结构

### **🎯 DDD分层包命名规范**
```java
// ✅ 标准包结构
com.ensign.web.{domain}.{subdomain}.application.service
com.ensign.web.{domain}.{subdomain}.application.convertor
com.ensign.web.{domain}.{subdomain}.application.pojo.cmd
com.ensign.web.{domain}.{subdomain}.application.pojo.query
com.ensign.web.{domain}.{subdomain}.application.pojo.response

com.ensign.web.{domain}.{subdomain}.domain.model
com.ensign.web.{domain}.{subdomain}.domain.repository
com.ensign.web.{domain}.{subdomain}.domain.service

com.ensign.web.{domain}.{subdomain}.infrastructure.persistence.entity
com.ensign.web.{domain}.{subdomain}.infrastructure.persistence.mapper
com.ensign.web.{domain}.{subdomain}.infrastructure.persistence.repository

com.ensign.web.{domain}.{subdomain}.interfaces.controller

// 具体示例：
com.ensign.web.tenant.role.application.service.TenantRoleCommandService
com.ensign.web.tenant.role.domain.model.TenantRole
com.ensign.web.tenant.role.infrastructure.persistence.entity.TenantRoleEntity
```

### **📋 文件命名标准**
```java
// ✅ 应用层命名
CreateTenantRoleCmd.java             // 创建命令
UpdateTenantRoleCmd.java             // 更新命令
TenantRoleQuery.java                 // 查询对象
TenantRoleResponse.java              // 响应对象
TenantRoleDetailResponse.java        // 详情响应对象
TenantRoleCommandService.java        // 命令服务
TenantRoleQueryService.java          // 查询服务
TenantRoleAppConvertor.java          // 应用层转换器

// ✅ 领域层命名
TenantRole.java                      // 聚合根
TenantRoleDomainService.java         // 领域服务
ITenantRoleRepository.java           // 仓储接口

// ✅ 基础设施层命名
TenantRoleEntity.java                // 数据库实体
TenantRoleMapper.java                // MyBatis映射器
TenantRoleRepositoryImpl.java        // 仓储实现
TenantRoleEntityConvertor.java       // 实体转换器

// ✅ 接口层命名
TenantRoleController.java            // REST控制器
```

### **🚀 服务分离模式**
```java
// ✅ CQRS模式：严格分离命令和查询服务
@Service
@Transactional(readOnly = true)
public class TenantRoleCommandService {  // 写操作

    @Transactional(rollbackFor = Exception.class)
    public TenantRole createRole(CreateTenantRoleCmd cmd) { }

    @Transactional(rollbackFor = Exception.class)
    public void updateRole(UpdateTenantRoleCmd cmd) { }

    @Transactional(rollbackFor = Exception.class)
    public void deleteRole(Long id) { }
}

@Service
@Transactional(readOnly = true)
public class TenantRoleQueryService {    // 读操作
    public TenantRole getById(Long id) { }
    public PageResponse<TenantRole> pageQuery(TenantRoleQuery query) { }
    public List<TenantRole> listByTenantId(Long tenantId) { }
}

// ✅ 控制器中分别注入
@RestController
@RequiredArgsConstructor
public class TenantRoleController {
    private final TenantRoleCommandService commandService;
    private final TenantRoleQueryService queryService;

    // 最多2个方法：分页查询 + 详情查询
    @GetMapping
    public ApiResponse<PageResponse<TenantRoleResponse>> pageQuery(@Valid TenantRoleQuery query) { }

    @GetMapping("/{id}")
    public ApiResponse<TenantRoleDetailResponse> getById(@PathVariable Long id) { }
}
```

## Project Structure

### Backend Services

#### always use MySqlTool mcp tool to manage databases and tables

- **services/**: Core microservices
  - `tenant-service` - Multi-tenant management and IAM
  - `authentication-service` - User authentication with MFA
  - `freight-service` - Freight and carrier management
  - `billing-service` - Billing and charge management
  - `shipping-service` - Shipment booking and tracking
  - `product-service` - Product and SKU management
  - `crm-service` - Customer relationship management
  - `system-service` - System configurations
  - `ai-service` - AI integration services
  - `freight-rate-service` - Freight rate calculations
  - `schedule-client-service` - Schedule management

- **gateways/**: API gateway services
  - `web-gateway` - Central API gateway with routing

- **starters/**: Reusable Spring Boot starter components
  - Common libraries for auth, cache, data access, RPC, etc.

### Frontend Applications
- **frontend/saas-tenant** - Customer portal (React 19, TanStack Router)
- **frontend/saas-admin** - Administrator portal
- **frontend/saas-partner** - Business partner portal

## Development Standards

### Architecture Requirements
- **DDD Layered Architecture**: Strict separation between interfaces, application, domain, and infrastructure layers
- **Multi-Tenancy**: All entities must include `tenant_id` for automatic tenant isolation
- **API Response Format**: Use `ApiResponse<T>` wrapper for all API responses
- **Test Coverage**: Unit tests ≥85%, integration tests ≥70%

### 🏗️ 后端开发标准 (遵循 .cursor/rules/java/)

#### **🎯 DDD架构核心原则**
- **严格四层架构**：interfaces → application → domain ← infrastructure
- **聚合根设计**：ProductSku作为产品领域核心聚合根，Product作为轻量级数据容器
- **聚合边界**：基于业务逻辑复杂性和状态管理独立性定义
- **一个聚合一个仓储**：确保逻辑内聚性，避免过度设计

#### **⚠️ 异常处理强制规范**
- **统一异常体系**：使用`BizException` + 领域特定`ResultCode`枚举
- **禁止原生异常**：绝不使用`IllegalArgumentException`、`RuntimeException`
- **错误码规范**：`{DOMAIN}_{OBJECT}_{ACTION}_{REASON}`命名模式
- **状态码标准**：400/401/403/404/409/422/500正确选择

#### **🗄️ 实体继承与数据库设计**
- **基类选择**：根据业务特性选择合适基类（MinimalEntity/SimpleEntity/TenantScopedEntity等）
- **审计字段**：企业级完整审计字段自动填充（时间/用户/操作/版本审计）
- **多租户隔离**：依赖MyBatis Plus自动填充，禁止手动传递tenant_id
- **布尔字段**：避免'is'前缀（防止RPC解析错误）

#### **� 多租户与业务伙伴上下文处理 (Rule-ID: ENT-001, ENT-002)**
- **业务伙伴自服务上下文**：业务伙伴处理自己的操作时，不得显式添加partner_id或tenant_id到查询/CRUD操作中，应由认证上下文自动处理
- **租户用户访问业务伙伴上下文**：租户用户对业务伙伴数据进行操作时，不得显式添加tenant_id，但必须在相关类/实体中包含partner_id
- **认证上下文使用**：所有上下文处理必须使用AuthContextUtil类（位于ensign-spring-boot-starter-util）

#### **�🔄 MapStruct转换器规范**
- **命名标准**：`{Entity}AppConvertor`、`{Entity}EntityConvertor`、`{Service}RemoteConvertor`
- **配置标准**：`componentModel = "spring"`, `unmappedTargetPolicy = ReportingPolicy.IGNORE`
- **转换方法**：`toDomain()`, `toEntity()`, `toResponse()`, `toDetailResponse()`
- **复杂映射**：使用`@Mapping`注解和`@Named`自定义方法

#### **🔢 枚举使用规范 (Rule-ID: ENUM-001 to ENUM-005)**
- **BaseEnum接口实现**：所有枚举必须实现BaseEnum接口，使用@JsonValue标记getCode()方法
- **数据库存储**：使用TINYINT UNSIGNED存储Integer类型code，避免MySQL ENUM类型
- **API传输**：请求支持code和name两种格式，响应序列化为code值
- **转换器处理**：MapStruct转换器包含枚举错误处理和批量转换异常处理
- **验证配置**：提供findByCode()，findByName静态方法，包含完整字段（code, name, description）如
```java
  /**
     * 根据代码查找枚举
     */
    public static Optional<BillingVariableTypeEnum> findByCode(Integer code) {
        if (code == null) {
            return Optional.empty();
        }
        for (BillingVariableTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return Optional.of(type);
            }
        }
        return Optional.empty();
    }

    /**
     * 根据名称查找枚举
     */
    public static Optional<BillingVariableTypeEnum> findByName(String name) {
        if (name == null) {
            return Optional.empty();
        }
        for (BillingVariableTypeEnum type : values()) {
            if (type.getName().equals(name)) {
                return Optional.of(type);
            }
        }
        return Optional.empty();
    }
```

#### **🏪 仓储模式与MyBatis Plus**
- **简化仓储**：仅包含核心查询方法（save/getById/pageQuery/list/exists/delete）
- **自动填充**：依赖MyBatis Plus处理tenant_id和审计字段
- **查询构建**：使用`LambdaQueryWrapper`，避免XML配置
- **分页响应**：使用`PageResponse<T>`统一分页格式

#### **🚀 CQRS服务分离**
- **命令服务**：`{Entity}CommandService`处理写操作，使用事务
- **查询服务**：`{Entity}QueryService`处理读操作，只读事务
- **控制器限制**：最多2个方法（分页查询 + 详情查询）
- **依赖注入**：控制器分别注入命令和查询服务

#### **📋 命名约定标准**
- **类名**：UpperCamelCase（如`TenantRoleService`）
- **命令对象**：`Create{Entity}Cmd`, `Update{Entity}Cmd`
- **查询对象**：`{Entity}Query`, `{Entity}PageQuery`
- **响应对象**：`{Entity}Response`, `{Entity}DetailResponse`
- **包结构**：`com.ensign.web.{domain}.{subdomain}.{layer}`

#### **🔧 API响应与认证**
- **统一响应**：使用`ApiResponse<T>`包装所有API响应
- **分页响应**：使用`PageResponse<T>`处理分页数据
- **认证上下文**：使用`AuthContextUtil`自动处理租户/伙伴过滤
- **参数验证**：使用Bean Validation，避免手动验证

# 🎨 前端开发指南

## 🎯 核心架构原则

### **CSR客户端渲染架构**
```typescript
// ✅ CSR数据获取策略
┌─────────────────┬─────────────────┬─────────────────┐
│   列表数据       │    详情数据      │   表单数据       │
│   (Client)      │   (Client)      │   (Client)      │
├─────────────────┼─────────────────┼─────────────────┤
│ • 分页列表       │ • 搜索结果       │ • 创建表单       │
│ • 过滤数据       │ • 详情页面       │ • 更新表单       │
│ • httpClients   │ • httpClients   │ • httpClients   │
└─────────────────┴─────────────────┴─────────────────┘
```

### **统一API调用层**
```typescript
// ✅ httpClients.ts：统一后端调用层（自动认证）
import api from '@/api/httpClients'

export const getOrganizationRateProfiles = async (
  params: QueryParams = {}
): Promise<ApiResponse<PageResponse<ProfileResponse>>> => {
  const queryParams = new URLSearchParams();
  if (params.organizationId) queryParams.append('organizationId', params.organizationId);

  const queryString = queryParams.toString();
  const url = queryString ? `${API_BASE_PATH}?${queryString}` : API_BASE_PATH;

  return api.get<ApiResponse<PageResponse<ProfileResponse>>>(url);
};
```

## 🏗️ 技术栈标准

### **核心技术组件**
- **React 19** + TypeScript strict mode
- **TanStack Router & Query** + Zustand
- **Shadcn UI** + Tailwind CSS 4.x + Origin UI
- **React Hook Form** + Zod + Vite 6.x

### **UI组件优先级**
```typescript
// ✅ 组件选择优先级
1. 现有组件 (existing components)
2. Shadcn UI (primary UI library)
3. Origin UI (advanced components)
4. 自定义组件 (custom components)

// ❌ 绝对禁止：Ant Design, Material UI
```

## 🔧 实现模式与最佳实践

### **数据获取模式**
```typescript
// ✅ TanStack Query Hooks
export const useOrganizationRateProfiles = (
  params: QueryParams = {},
  options: { enabled?: boolean } = {}
) => {
  return useQuery({
    queryKey: ['organization-rate-profiles', params],
    queryFn: () => getOrganizationRateProfiles(params),
    enabled: options.enabled ?? true,
    staleTime: 5 * 60 * 1000,
    select: (data) => data.data,
  });
};
```

### **表单验证模式**
```typescript
// ✅ React Hook Form + Zod
const createRoleSchema = z.object({
  roleCode: z.string().min(1, "Role code is required"),
  roleName: z.string().min(1, "Role name is required"),
})

type CreateRoleForm = z.infer<typeof createRoleSchema>

function CreateRoleForm() {
  const form = useForm<CreateRoleForm>({
    resolver: zodResolver(createRoleSchema),
  })

  const createMutation = useMutation({
    mutationFn: createRole,
    onSuccess: () => {
      toast.success("Role created successfully")
      queryClient.invalidateQueries({ queryKey: ['roles'] })
    },
  })
}
```

## ⚠️ 关键禁忌事项

### **🚫 前端开发禁忌**
```typescript
// ❌ 绝对禁止：直接使用axios
import axios from 'axios'
const data = await axios.get('/api/users')  // 错误：绕过认证

// ❌ 绝对禁止：使用Server Functions
import { createServerFn } from '@tanstack/start'
const fetchUsers = createServerFn(...)  // 错误：不适用CSR

// ❌ 绝对禁止：使用Ant Design或Material UI
import { Button } from 'antd'  // 错误：使用禁止的UI库

// ✅ 正确做法：使用httpClients.ts + Shadcn UI
import api from '@/api/httpClients'
import { Button } from '@/components/ui/button'
```

## 📋 前端开发检查清单

### **🏗️ 架构检查**
```yaml
CSR架构:
  ✅ 使用httpClients.ts统一API调用
  ✅ TanStack Query处理数据缓存
  ✅ TanStack Router类型安全路由
  ✅ 禁用Server Functions和serverApi

组件设计:
  ✅ Shadcn UI优先级使用
  ✅ 响应式Mobile First设计
  ✅ 统一data-table组件
  ✅ 英文UI文本标准

数据处理:
  ✅ React Hook Form + Zod验证
  ✅ useMutation处理数据变更
  ✅ 错误处理和toast通知
  ✅ 查询缓存失效管理
```

# 🚀 快速开发命令

## 后端开发命令
```bash
# 快速开发构建（推荐）
./gradlew devBuild

# 生产构建
./gradlew prodBuild

# 运行所有测试
./gradlew test

# 运行特定服务测试
./gradlew :services:tenant-service:test

# 本地运行单个服务
./gradlew :services:tenant-service:bootRun
./gradlew :gateways:web-gateway:bootRun

# 数据库管理（使用MySQL MCP工具）
mysql_query_mysql "SHOW DATABASES;"
mysql_health_check_mysql
```

## 前端开发命令
```bash
# 开发服务器（saas-tenant端口3000）
cd frontend/saas-tenant && npm run dev

# 生产构建
npm run build

# 代码检查和格式化
npm run lint
npm run format

# 类型检查
npm run type-check
```

# 📚 详细文档引用

## 🏗️ 后端开发规范
@.cursor/docs/java/04_DDD_STRUCTURE.md                    # DDD四层架构详细规范
@.cursor/docs/java/05_ERROR_HANDLING_CORE.md              # 错误处理核心规范
@.cursor/docs/java/06_PARAMETER_VALIDATION.md             # 参数验证规范
@.cursor/docs/java/07_EXCEPTION_BEST_PRACTICES.md         # 异常处理最佳实践
@.cursor/docs/java/13_MYSQL_DATABASE_DESIGN.md            # MySQL数据库设计规范
@.cursor/docs/java/entity-inheritance-usage-examples.md   # 实体继承使用示例
@.cursor/docs/java/AUDIT_FIELDS_MIGRATION_GUIDE.md        # 审计字段迁移指南
@.cursor/docs/java/ERROR_CODE_BEST_PRACTICES.md           # 错误代码最佳实践
@.cursor/docs/java/mybatis-plus-best-practices.md         # MyBatis Plus最佳实践
@.cursor/docs/java/02-repository-patterns.md              # 仓储模式实现

## 🔄 转换器和规则
@.cursor/rules/java/core/mapstruct-convertor.mdc          # MapStruct转换器规范
@.cursor/rules/java/core/enum-usage.mdc                   # 枚举使用规范
@.augment/rules/imported/core/entity-inheritance.md       # 实体继承导入规范

## 🎨 前端开发规范
@.cursor/docs/react/04-frontend-development-guide.md      # 前端开发指南
@.cursor/docs/react/06-data-query-patterns.md             # 数据查询模式
@.cursor/docs/react/03-features-architecture-guide.md     # 功能架构指南
@.cursor/docs/react/01-ddd-directory-structure-standards.md # DDD目录结构标准

## 🔧 项目配置
@package.json                                             # 前端项目配置
@gradle.properties                                        # Gradle构建配置
@settings.gradle                                          # Gradle模块配置

## Domain Knowledge

### Logistics Domain
- **Freight Rate Management**: Complex hierarchical charge structures with multi-currency support
- **Multi-modal Transportation**: Support for air, sea, and land freight
- **Customs Clearance**: Comprehensive customs declaration support
- **Supply Chain**: Integration with suppliers and customer portals

### Product Management
- **Supplier Management**: Comprehensive supplier lifecycle management with staged information updates
  - **Entity Scope**: Use `PartnerScopedEntity` for cross-tenant business partner data (suppliers, products)
  - **Information Categories**: Basic → Contact → Financial → Address → Rating (staged update approach)
  - **Business Partner Context**: Automatic filtering using `AuthContextUtil.getBusinessPartnerUser().getPartnerId()`
  - **Tenant-to-Partner Queries**: Tenant users can query specific business partner data when authorized
  - **Repository Pattern**: Simplified repositories with automatic tenant/partner filtering via MyBatis Plus
  - **Query Types**: Essential business queries only (page, detail, active list, basic validation)

### Authentication & Security
- **Multi-Factor Authentication**: Progressive MFA with timing attack prevention
- **Role-Based Access Control**: Comprehensive IAM system comparable to cloud providers
- **Tenant Isolation**: Automatic tenant-scoped data access
- **Business Partner Context**: `AuthContextUtil` handles user context for tenant/partner data access
- **Security Auditing**: Complete audit trail for all operations

### Business Operations
- **Multi-Tenant SaaS**: Native multi-tenant support with organization management
- **Billing Integration**: Complex billing rules with time-based charging
- **Customer Portal**: Self-service capabilities for end customers
- **Partner Management**: Supplier relationship management with regional pricing

## Technology Stack

### Backend
- **Java 21** with Spring Boot 3.5.3
- **Spring Cloud 2025.0.0** with Alibaba components
- **MyBatis Plus 3.5.12** for database operations
- **MySQL 9.3.0** with optimized indexing
- **Redis/Redisson** for caching and sessions
- **RocketMQ 2.3.3** for message queuing
- **Sa-Token 1.44.0** for authentication
- **MapStruct 1.6.3** for entity mapping

### Frontend
- **React 19** with TypeScript strict mode
- **TanStack Router & Query** for routing and state management
- **Shadcn UI & Radix UI** for component library
- **Tailwind CSS 4.x** for styling
- **React Hook Form + Zod** for form validation
- **Vite 6.x** for build tooling
- **i18next** for internationalization

### Infrastructure
- **Docker Compose** for local development
- **Gradle** with build optimization and caching
- **Aliyun ECS** for production deployment
- **MySQL 9.x** with advanced features

## Common Development Tasks

### Running Tests
```bash
# All tests
./gradlew test

# Service-specific tests
./gradlew :services:tenant-service:test

# Frontend tests
cd frontend/saas-tenant && npm test
```

## MCP Integration

This project uses multiple MCP (Model Context Protocol) servers:
- **Context7**: For library documentation and project rules
- **MySQL**: For database operations and optimization
- **Filesystem**: For advanced file operations
- **IDE**: For development tool integration
- **Sequential Thinking**: For complex problem solving

When working with libraries, always use Context7 MCP tools (`resolve-library-id`, `get-library-docs`) for accurate documentation.

## Performance Optimization

## Java Backend Development Rules (.cursor/rules/java/)

### Critical Rules (Auto-Applied)
When developing backend code, follow these **mandatory** Cursor rules:

#### 1. Exception Handling (Rule-ID: EXC-001, EXC-002)
```java
// ✅ Required: Use BizException with domain-specific ResultCode
import com.ensign.starter.common.exception.BizException;

public class TenantRoleService {
    public TenantRole createRole(CreateTenantRoleCmd cmd) {
        if (roleRepository.existsByRoleCode(cmd.getRoleCode())) {
            throw new BizException(RoleResultCode.ROLE_CODE_EXISTS, cmd.getRoleCode());
        }
    }
}

// ❌ Forbidden: Raw exceptions
throw new IllegalArgumentException("参数错误");  // Never do this
throw new RuntimeException("业务处理失败");      // Never do this
```

#### 2. DDD Layer Structure (Rule-ID: STRUCT-001)
```
services/{service-name}/src/main/java/com/ensign/web/{domain}/{subdomain}/
├── interfaces/controller/        # REST controllers (max 2 methods)
├── application/service/          # Command/Query services
├── domain/model/                 # Aggregates and entities
└── infrastructure/persistence/   # Repository implementations
```

#### 3. Service Separation (Rule-ID: STRUCT-002)
```java
// ✅ Required: Separate Command and Query services
@Service
public class TenantRoleCommandService {  // Write operations
    public TenantRole createRole(CreateTenantRoleCmd cmd) { }
    public void updateRole(UpdateTenantRoleCmd cmd) { }
}

@Service  
public class TenantRoleQueryService {    // Read operations
    public TenantRoleResponse getRole(TenantRoleQuery query) { }
    public PageResult<TenantRoleResponse> pageRoles(TenantRolePageQuery query) { }
}
```

#### 4. Naming Standards (Rule-ID: NAME-001 to NAME-006)
- **Commands**: `Create{Entity}Cmd`, `Update{Entity}Cmd`, `Delete{Entity}Cmd`
- **Queries**: `{Entity}Query`, `{Entity}PageQuery`
- **Responses**: `{Entity}Response`, `{Entity}DetailResponse`
- **Convertors**: `{Entity}EntityConvertor`, `{Entity}AppConvertor`, `{Entity}RemoteConvertor`
- **Controllers**: `{Entity}Controller` (command), `{Entity}QueryController` (query)

#### 5. Entity Design (Rule-ID: ENT-001, ENT-002)
```java
// ✅ Required: All entities extend BaseEntity
@Entity
@Table(name = "tenant_role")
public class TenantRole extends BaseEntity {
    // Inherits: id, tenantId, createdAt, updatedAt, isDeleted
}

// ✅ Required: No 'is' prefix for boolean fields (RPC compatibility)
private Boolean active;     // ✅ Correct
private Boolean isActive;   // ❌ Wrong - causes RPC parsing errors
```

### Auto-Triggering Rules
These rules automatically apply based on file patterns:
- `**/*Controller.java` → Interface layer + API + Validation rules
- `**/*CommandService.java` → Application layer + Command rules  
- `**/*QueryService.java` → Application layer + Query rules
- `**/*DomainService.java` → Domain layer rules
- `**/*RepositoryImpl.java` → Infrastructure + MyBatis-Plus rules
- `**/*Entity.java` → Entity inheritance rules
- `**/*Enum.java` → Enum usage rules
- `**/*Convertor.java` → MapStruct rules

### Rule Priority System
1. **Critical**: Exception handling, Code structure, Entity inheritance
2. **High**: API patterns, Enum usage, MapStruct convertors  
3. **Medium**: Validation, Naming conventions, Database design

## 📚 详细文档引用

### 🏗️ 后端开发规范
@.cursor/docs/java/04_DDD_STRUCTURE.md                    # DDD四层架构详细规范
.cursor/docs/java/05_ERROR_HANDLING.md                   # 错误处理和异常规范
@.cursor/docs/java/02_DATABASE.md                         # 数据库设计规范
@.cursor/docs/java/13_MYSQL_DATABASE_DESIGN.md            # MySQL数据库设计规范
@.cursor/docs/java/entity-inheritance-usage-examples.md   # 实体继承使用示例
@.cursor/docs/java/AUDIT_FIELDS_MIGRATION_GUIDE.md        # 审计字段迁移指南
@.cursor/docs/java/ERROR_CODE_BEST_PRACTICES.md           # 错误代码最佳实践
@.cursor/docs/java/mybatis-plus-best-practices.md         # MyBatis Plus最佳实践
@.cursor/docs/java/02-repository-patterns.md              # 仓储模式实现
@.cursor/docs/java/12_ENTITY_MAPPING.md                   # 实体映射规范

### 🔄 转换器和规则
@.cursor/rules/java/core/mapstruct-convertor.mdc          # MapStruct转换器规范
@.cursor/rules/java/core/entity-inheritance.mdc           # 实体继承架构规范
@.augment/rules/imported/core/entity-inheritance.md       # 实体继承导入规范
@.augment/rules/imported/core/mapstruct-convertor.md      # MapStruct转换器导入规范

### 🎨 前端开发规范
@.cursor/docs/react/04-frontend-development-guide.md      # 前端开发指南
@.cursor/docs/react/06-data-query-patterns.md             # 数据查询模式
@.cursor/docs/react/03-features-architecture-guide.md     # 功能架构指南
@.cursor/docs/react/01-ddd-directory-structure-standards.md # DDD目录结构标准

### 🔧 项目配置
@package.json                                             # 前端项目配置
@gradle.properties                                        # Gradle构建配置
@settings.gradle                                          # Gradle模块配置

## 🔧 Key Conventions

### Naming Standards
- **Classes**: UpperCamelCase (e.g., `TenantRoleService`)
- **Methods/Variables**: lowerCamelCase
- **Constants**: UPPER_SNAKE_CASE
- **Packages**: lowercase with semantic meaning

### Git Workflow
- **Main Branch**: `develop`
- **Feature Branches**: Follow standard Git Flow
- **Commit Messages**: Use conventional commits format

### Error Handling
- Use domain-specific error codes
- Provide English error messages
- Implement comprehensive logging and monitoring

# 🎯 内存管理与开发指南

## 📋 开发检查清单

### **🏗️ 后端开发检查清单**
```yaml
DDD架构检查:
  ✅ ProductSku作为产品领域核心聚合根
  ✅ 四层架构严格分离（interfaces→application→domain←infrastructure）
  ✅ CQRS模式命令查询分离
  ✅ 一个聚合一个仓储原则

实体继承检查:
  ✅ 根据业务特性选择合适基类（TenantScoped/PartnerScoped等）
  ✅ 审计字段自动填充配置
  ✅ 多租户数据隔离（避免手动tenant_id）
  ✅ 布尔字段避免'is'前缀

多租户上下文:
  ✅ 业务伙伴自服务不显式添加partner_id/tenant_id
  ✅ 租户用户访问业务伙伴数据必须包含partner_id
  ✅ 使用AuthContextUtil处理所有上下文
  ✅ 避免手动传递认证参数

枚举使用:
  ✅ 实现BaseEnum接口，使用@JsonValue注解
  ✅ 数据库使用TINYINT UNSIGNED存储
  ✅ API支持code和name两种格式
  ✅ 转换器包含枚举错误处理

转换器检查:
  ✅ MapStruct标准配置（componentModel="spring"）
  ✅ 命名规范（AppConvertor/EntityConvertor/RemoteConvertor）
  ✅ 复杂映射使用@Mapping注解
  ✅ 错误处理和空值检查

异常处理检查:
  ✅ 使用BizException + 领域特定ResultCode
  ✅ 错误码语义化命名（DOMAIN_OBJECT_ACTION_REASON）
  ✅ HTTP状态码正确选择
  ✅ 全局异常处理配置
```

### **🎨 前端开发检查清单**
```yaml
CSR架构检查:
  ✅ 使用httpClients.ts统一API调用（禁用axios直接调用）
  ✅ TanStack Query处理数据缓存（优先useQuery）
  ✅ TanStack Router类型安全路由
  ✅ 禁用Server Functions和serverApi

组件设计检查:
  ✅ Shadcn UI优先级使用（禁用Ant Design/Material UI）
  ✅ 响应式Mobile First设计（320px→768px→1024px+）
  ✅ 统一data-table组件使用
  ✅ 英文UI文本标准

数据处理检查:
  ✅ React Hook Form + Zod验证
  ✅ useMutation处理数据变更
  ✅ 错误处理和toast通知
  ✅ 查询缓存失效管理（queryClient.invalidateQueries）
```

## 🔧 MCP工具使用最佳实践

### **🧠 Sequential Thinking优先使用场景**
```yaml
必须使用Sequential Thinking的场景:
  ✅ 复杂业务逻辑分析和设计
  ✅ 多步骤架构决策和规划
  ✅ DDD聚合边界设计
  ✅ 数据库设计和优化方案
  ✅ 前端架构重构规划
  ✅ 错误处理机制设计
  ✅ 性能优化策略制定

使用原则:
  - 所有复杂分析场景都应优先使用Sequential Thinking
  - 不仅限于"complex problem solving"
  - 用于多步骤思考和决策验证
  - 帮助确保方案的完整性和正确性
```

### **🗄️ MySQL工具使用规范**
```yaml
数据库操作场景:
  ✅ 使用mysql_query_mysql执行查询
  ✅ 使用mysql_health_check_mysql检查性能
  ✅ 使用mysql_analyze_query_mysql优化查询
  ✅ 使用mysql_optimize_indexes_mysql索引优化

注意事项:
  - 始终使用MCP MySQL工具而非手动SQL
  - 优先使用工具进行数据库设计验证
  - 利用工具进行性能分析和优化
```
