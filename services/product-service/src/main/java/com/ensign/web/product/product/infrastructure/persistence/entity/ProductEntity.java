package com.ensign.web.product.product.infrastructure.persistence.entity;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ensign.starter.common.enums.product.ProductTypeEnum;
import com.ensign.starter.data.mysql.model.PartnerScopedEntity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Program: ensign-platform
 * @Author: RickyHuang
**/
/**
 * 商品主表（最小化设计）
 * ：继承PartnerScopedEntity基类，自动处理审计字段
 */
@Data
@EqualsAndHashCode(callSuper=true)
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`product`")
public class ProductEntity extends PartnerScopedEntity {

    /**
     * 商品编码（数据载体标识）
     */
    @TableField(value = "`product_code`")
    private String productCode;

    /**
     * 商品分类ID
     */
    @TableField(value = "`category_id`")
    private Long categoryId;

    /**
     * 品牌ID
     */
    @TableField(value = "`brand_id`")
    private Long brandId;

    /**
     * 分类名称（用于Excel导入）
     */
    @TableField(value = "`category_name`")
    private String categoryName;

    /**
     * 品牌名称（用于Excel导入）
     */
    @TableField(value = "`brand_name`")
    private String brandName;

    /**
     * 商品类型：1-单规格，2-多规格，3-组合商品
     */
    @TableField(value = "`product_type`")
    private ProductTypeEnum productType;

    // ========== 关联对象 ==========
    // 注意：这些字段不存储在数据库中，仅用于ORM关联查询

    /**
     * 商品SKU列表（一对多关联）
     */
    @TableField(exist = false)
    private List<ProductSkuEntity> skus;

    /**
     * 组合商品关系列表（一对多关联）
     */
    @TableField(exist = false)
    private List<ProductSkuComboEntity> comboRelations;

    // ========== 审计字段说明 ==========
    // 以下字段由PartnerScopedEntity基类提供，无需手动定义：
    // - tenant_id (租户ID)
    // - partner_id (业务伙伴ID)
    // - created_at (创建时间)
    // - updated_at (更新时间)
    // - created_by (创建人ID)
    // - updated_by (更新人ID)
    // - created_by_type (创建人类型)
    // - updated_by_type (更新人类型)
    // - operation_ip (操作IP)
    // - user_agent (用户代理)
    // - version (乐观锁版本号)
    // - is_deleted (逻辑删除标识)

    // ===============================
    // 业务方法
    // ===============================

    /**
     * 验证商品编码格式
     */
    public boolean isValidProductCode() {
        return productCode != null &&
               productCode.trim().length() >= 2 &&
               productCode.trim().length() <= 50 &&
               productCode.matches("^[A-Z0-9_-]+$");
    }

    /**
     * 验证是否为单规格商品
     */
    public boolean isSingleSpec() {
        return ProductTypeEnum.SINGLE_SPEC.equals(productType);
    }

    /**
     * 验证是否为多规格商品
     */
    public boolean isMultiSpec() {
        return ProductTypeEnum.MULTI_SPEC.equals(productType);
    }

    /**
     * 验证是否为组合商品
     */
    public boolean isCombo() {
        return ProductTypeEnum.COMBO.equals(productType);
    }

    /**
     * 获取SKU数量
     */
    public int getSkuCount() {
        return skus != null ? skus.size() : 0;
    }

    /**
     * 检查是否有SKU数据
     */
    public boolean hasSkus() {
        return skus != null && !skus.isEmpty();
    }

    /**
     * 检查是否有组合关系数据
     */
    public boolean hasComboRelations() {
        return comboRelations != null && !comboRelations.isEmpty();
    }

    /**
     * 验证SKU数量是否符合商品类型
     */
    public boolean isValidSkuCount() {
        int skuCount = getSkuCount();

        if (isSingleSpec()) {
            return skuCount == 1;
        } else if (isMultiSpec()) {
            return skuCount > 1;
        } else if (isCombo()) {
            return skuCount >= 1 && hasComboRelations();
        }
        return false;
    }

    /**
     * 验证实体完整性
     */
    public boolean isValid() {
        return isValidProductCode() &&
               productType != null &&
               hasSkus() &&
               isValidSkuCount();
    }
}