package com.ensign.web.product.product.interfaces.web;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ensign.starter.common.exception.BizException;
import com.ensign.starter.common.model.ApiResponse;
import com.ensign.starter.util.security.AccessType;
import com.ensign.starter.util.security.AuthContextUtil;
import com.ensign.starter.web.servlet.response.PageResponse;
import com.ensign.web.product.product.application.pojo.cmd.CreateComboProductCmd;
import com.ensign.web.product.product.application.pojo.cmd.CreateMultiSpecProductCmd;
import com.ensign.web.product.product.application.pojo.cmd.CreateSingleSpecProductCmd;
import com.ensign.web.product.product.application.pojo.query.ProductPageQuery;
import com.ensign.web.product.product.application.pojo.response.ProductDetailResponse;
import com.ensign.web.product.product.application.pojo.response.ProductResponse;
import com.ensign.web.product.product.application.service.ProductCommandService;
import com.ensign.web.product.product.application.service.ProductQueryService;
import com.ensign.web.product.product.domain.enums.ProductResultCode;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 商品管理控制器
 * 支持多角色访问：业务伙伴自服务 + 租户代理管理
 * 
 * URL设计：
 * - 业务伙伴自服务：/api/partner/products/**
 * - 租户代理管理：/api/tenant/partners/{partnerId}/products/**
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "商品管理", description = "商品CRUD操作，支持多角色访问")
public class ProductController {

    private final ProductCommandService productCommandService;
    private final ProductQueryService productQueryService;

    // ===============================
    // 业务伙伴自服务接口
    // ===============================

    /**
     * 业务伙伴创建单规格商品
     */
    @PostMapping("/api/partner/products/single-spec")
    @Operation(summary = "创建单规格商品", description = "业务伙伴创建单规格商品")
    public ApiResponse<Long> createSingleSpecProduct(@Valid @RequestBody CreateSingleSpecProductCmd cmd) {
        validatePartnerAccess();

        Long productId = productCommandService.createSingleSpecProduct(cmd);
        log.info("业务伙伴创建单规格商品成功，商品ID：{}", productId);

        return ApiResponse.ok(productId);
    }

    /**
     * 业务伙伴创建多规格商品
     */
    @PostMapping("/api/partner/products/multi-spec")
    @Operation(summary = "创建多规格商品", description = "业务伙伴创建多规格商品")
    public ApiResponse<Long> createMultiSpecProduct(@Valid @RequestBody CreateMultiSpecProductCmd cmd) {
        validatePartnerAccess();

        Long productId = productCommandService.createMultiSpecProduct(cmd);
        log.info("业务伙伴创建多规格商品成功，商品ID：{}", productId);

        return ApiResponse.ok(productId);
    }

    /**
     * 业务伙伴创建组合商品
     */
    @PostMapping("/api/partner/products/combo")
    @Operation(summary = "创建组合商品", description = "业务伙伴创建组合商品")
    public ApiResponse<Long> createComboProduct(@Valid @RequestBody CreateComboProductCmd cmd) {
        validatePartnerAccess();

        Long productId = productCommandService.createComboProduct(cmd);
        log.info("业务伙伴创建组合商品成功，商品ID：{}", productId);

        return ApiResponse.ok(productId);
    }

    /**
     * 业务伙伴分页查询商品
     */
    @GetMapping("/api/partner/products")
    @Operation(summary = "分页查询商品", description = "业务伙伴分页查询自己的商品")
    public ApiResponse<PageResponse<ProductResponse>> getPartnerProducts(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") Integer pageSize,
            @Parameter(description = "商品编码") @RequestParam(required = false) String productCode,
            @Parameter(description = "商品类型") @RequestParam(required = false) Integer productType) {

        validatePartnerAccess();

        ProductPageQuery query = new ProductPageQuery();
        query.setPageNum(pageNum);
        query.setPageSize(pageSize);
        query.setProductCode(productCode);
        query.setProductType(productType);

        PageResponse<ProductResponse> result = productQueryService.pageProducts(query);
        return ApiResponse.ok(result);
    }

    /**
     * 业务伙伴查询商品详情
     */
    @GetMapping("/api/partner/products/{productId}")
    @Operation(summary = "查询商品详情", description = "业务伙伴查询商品详情")
    public ApiResponse<ProductDetailResponse> getPartnerProductDetail(
            @Parameter(description = "商品ID") @PathVariable Long productId) {

        validatePartnerAccess();

        ProductDetailResponse result = productQueryService.getProductDetail(productId);
        if (result == null) {
            throw new BizException(ProductResultCode.PRODUCT_NOT_FOUND);
        }

        return ApiResponse.ok(result);
    }

    // ===============================
    // 租户代理管理接口
    // ===============================

    /**
     * 租户代理创建单规格商品
     */
    @PostMapping("/api/tenant/partners/{partnerId}/products/single-spec")
    @Operation(summary = "租户代理创建单规格商品", description = "租户帮助业务伙伴创建单规格商品")
    public ApiResponse<Long> createSingleSpecProductForPartner(
            @Parameter(description = "业务伙伴ID") @PathVariable Long partnerId,
            @Valid @RequestBody CreateSingleSpecProductCmd cmd) {
        
        validateTenantAccessForPartner(partnerId);
        
        Long productId = productCommandService.createSingleSpecProduct(cmd);
        log.info("租户代理为业务伙伴{}创建单规格商品成功，商品ID：{}", partnerId, productId);
        
        return ApiResponse.ok(productId);
    }

    /**
     * 租户代理创建多规格商品
     */
    @PostMapping("/api/tenant/partners/{partnerId}/products/multi-spec")
    @Operation(summary = "租户代理创建多规格商品", description = "租户帮助业务伙伴创建多规格商品")
    public ApiResponse<Long> createMultiSpecProductForPartner(
            @Parameter(description = "业务伙伴ID") @PathVariable Long partnerId,
            @Valid @RequestBody CreateMultiSpecProductCmd cmd) {

        validateTenantAccessForPartner(partnerId);

        Long productId = productCommandService.createMultiSpecProduct(cmd);
        log.info("租户代理为业务伙伴{}创建多规格商品成功，商品ID：{}", partnerId, productId);

        return ApiResponse.ok(productId);
    }

    /**
     * 租户代理创建组合商品
     */
    @PostMapping("/api/tenant/partners/{partnerId}/products/combo")
    @Operation(summary = "租户代理创建组合商品", description = "租户帮助业务伙伴创建组合商品")
    public ApiResponse<Long> createComboProductForPartner(
            @Parameter(description = "业务伙伴ID") @PathVariable Long partnerId,
            @Valid @RequestBody CreateComboProductCmd cmd) {
        
        validateTenantAccessForPartner(partnerId);
        
        Long productId = productCommandService.createComboProduct(cmd);
        log.info("租户代理为业务伙伴{}创建组合商品成功，商品ID：{}", partnerId, productId);
        
        return ApiResponse.ok(productId);
    }

    /**
     * 租户代理分页查询业务伙伴商品
     */
    @GetMapping("/api/tenant/partners/{partnerId}/products")
    @Operation(summary = "租户代理分页查询业务伙伴商品", description = "租户分页查询指定业务伙伴的商品")
    public ApiResponse<PageResponse<ProductResponse>> getTenantPartnerProducts(
            @Parameter(description = "业务伙伴ID") @PathVariable Long partnerId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") Integer pageSize,
            @Parameter(description = "商品编码") @RequestParam(required = false) String productCode,
            @Parameter(description = "商品类型") @RequestParam(required = false) Integer productType) {

        validateTenantAccessForPartner(partnerId);

        ProductPageQuery query = new ProductPageQuery();
        query.setPageNum(pageNum);
        query.setPageSize(pageSize);
        query.setProductCode(productCode);
        query.setProductType(productType);
        // 设置查询特定业务伙伴的商品
        query.setPartnerId(partnerId);

        PageResponse<ProductResponse> result = productQueryService.pageProducts(query);
        return ApiResponse.ok(result);
    }

    /**
     * 租户代理查询业务伙伴商品详情
     */
    @GetMapping("/api/tenant/partners/{partnerId}/products/{productId}")
    @Operation(summary = "租户代理查询业务伙伴商品详情", description = "租户查询指定业务伙伴的商品详情")
    public ApiResponse<ProductDetailResponse> getTenantPartnerProductDetail(
            @Parameter(description = "业务伙伴ID") @PathVariable Long partnerId,
            @Parameter(description = "商品ID") @PathVariable Long productId) {
        
        validateTenantAccessForPartner(partnerId);
        
        ProductDetailResponse result = productQueryService.getProductDetail(productId);
        if (result == null) {
            throw new BizException(ProductResultCode.PRODUCT_NOT_FOUND);
        }
        
        // 验证商品是否属于指定的业务伙伴
        if (!partnerId.equals(result.getPartnerId())) {
            throw new BizException(ProductResultCode.UNAUTHORIZED_ACCESS);
        }
        
        return ApiResponse.ok(result);
    }

    // ===============================
    // 权限验证方法
    // ===============================

    /**
     * 验证业务伙伴访问权限
     */
    private void validatePartnerAccess() {
        AccessType accessType = AuthContextUtil.getAccessType();
        if (accessType != AccessType.PARTNER_USER) {
            log.warn("非业务伙伴用户尝试访问业务伙伴接口，访问类型：{}", accessType);
            throw new BizException(ProductResultCode.UNAUTHORIZED_ACCESS);
        }
        
        // 记录访问日志
        Long partnerId = AuthContextUtil.getPartnerId();
        log.debug("业务伙伴访问验证通过，业务伙伴ID：{}", partnerId);
    }

    /**
     * 验证租户代理访问权限
     */
    private void validateTenantAccessForPartner(Long targetPartnerId) {
        AccessType accessType = AuthContextUtil.getAccessType();
        if (accessType != AccessType.TENANT) {
            log.warn("非租户用户尝试访问租户代理接口，访问类型：{}", accessType);
            throw new BizException(ProductResultCode.UNAUTHORIZED_ACCESS);
        }
        
        // 验证租户是否有权限管理该业务伙伴
        Long tenantId = AuthContextUtil.getTenantId();
        // TODO: 这里应该调用业务伙伴服务验证租户与业务伙伴的关系
        // boolean hasPermission = businessPartnerService.validateTenantPartnerRelation(tenantId, targetPartnerId);
        // if (!hasPermission) {
        //     throw new BizException(ProductResultCode.UNAUTHORIZED_ACCESS);
        // }
        
        log.debug("租户代理访问验证通过，租户ID：{}，目标业务伙伴ID：{}", tenantId, targetPartnerId);
    }
}
