package com.ensign.starter.util.security;

import com.ensign.starter.common.context.OrganizationContext;
import com.ensign.starter.common.model.ApiSession;
import com.ensign.starter.common.model.BusinessPartnerUser;
import com.ensign.starter.common.model.ClientUser;
import com.ensign.starter.common.model.PlatformAdminUser;
import com.ensign.starter.common.model.TenantAuthUser;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.function.Supplier;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

@Slf4j
public class AuthContextUtil {
  private static final ThreadLocal<ClientUser> CLIENT_USER_CONTEXT = new ThreadLocal<>();
  private static final ThreadLocal<BusinessPartnerUser> BUSINESS_PARTNER_USER_CONTEXT =
      new ThreadLocal<>();
  private static final ThreadLocal<TenantAuthUser> TENANT_USER_CONTEXT = new ThreadLocal<>();
  private static final ThreadLocal<Object> SESSION_CONTEXT = new ThreadLocal<>();
  private static final ThreadLocal<OrganizationContext> ORGANIZATION_CONTEXT = new ThreadLocal<>();
  private static final ThreadLocal<String> REQUEST_ID = new ThreadLocal<>();
  private static final ThreadLocal<Boolean> TENANT_FILTER_ENABLED = new ThreadLocal<>();
  private static final ThreadLocal<Boolean> INTERNAL_CALL = new ThreadLocal<>();
  private static final ThreadLocal<AccessType> ACCESS_TYPE_CONTEXT = new ThreadLocal<>();
  private static final ThreadLocal<PlatformAdminUser> PLATFORM_ADMIN_USER_CONTEXT =
      new ThreadLocal<>();

  @Data
  @Builder
  public static class UserSession {
    private String sessionId;
    private String ipAddress;
    private String userAgent;
    private LocalDateTime loginTime;
    private Map<String, String> additionalInfo;
  }

  // 设置访问类型
  public static void setAccessType(AccessType accessType) {
    ACCESS_TYPE_CONTEXT.set(accessType);
  }

  // 获取访问类型
  public static AccessType getAccessType() {
    return ACCESS_TYPE_CONTEXT.get();
  }

  // Web用户相关方法
  public static void setClientUserContext(ClientUser clientUser) {
    if (clientUser == null) {
      throw new IllegalArgumentException("Client user cannot be null");
    }
    CLIENT_USER_CONTEXT.set(clientUser);
    setAccessType(AccessType.CRM_USER);
  }

  public static ClientUser getClientUser() {
    ClientUser clientUser = CLIENT_USER_CONTEXT.get();
    if (clientUser == null) {
      throw new IllegalStateException("Client user context not set");
    }
    return clientUser;
  }

  public static void setApiSession(ApiSession session) {
    SESSION_CONTEXT.set(session);
  }

  public static Object getSession() {
    return SESSION_CONTEXT.get();
  }

  // 组织上下文管理
  public static void setOrganizationContext(OrganizationContext context) {
    ORGANIZATION_CONTEXT.set(context);
  }

  public static OrganizationContext getOrganizationContext() {
    OrganizationContext context = ORGANIZATION_CONTEXT.get();
    if (context == null) {
      throw new IllegalStateException("Organization context not set");
    }
    return context;
  }

  // 租户用户相关方法
  public static void setTenantUserContext(TenantAuthUser tenantUser) {
    if (tenantUser == null) {
      throw new IllegalArgumentException("Tenant user cannot be null");
    }
    TENANT_USER_CONTEXT.set(tenantUser);
    setAccessType(AccessType.TENANT);
  }

  public static TenantAuthUser getTenantUser() {
    TenantAuthUser tenantUser = TENANT_USER_CONTEXT.get();
    if (tenantUser == null) {
      throw new IllegalStateException("Tenant user context not set");
    }
    return tenantUser;
  }

  public static TenantAuthUser getTenantIdentity() {
    return getTenantUser();
  }

  // 用户类型判断
  public static boolean isTenantUser() {
    return TENANT_USER_CONTEXT.get() != null;
  }

  public static boolean isClientUser() {
    return CLIENT_USER_CONTEXT.get() != null;
  }

  // 租户过滤控制
  public static void enableTenantFilter() {
    TENANT_FILTER_ENABLED.set(true);
  }

  public static void disableTenantFilter() {
    TENANT_FILTER_ENABLED.set(false);
  }

  public static boolean isTenantFilterEnabled() {
    return !isInternalCall() && Boolean.TRUE.equals(TENANT_FILTER_ENABLED.get());
  }

  // 内部调用控制
  public static void setInternalCall(boolean isInternal) {
    INTERNAL_CALL.set(isInternal);
  }

  public static boolean isInternalCall() {
    return Boolean.TRUE.equals(INTERNAL_CALL.get());
  }

  // 请求ID管理
  public static void setRequestId(String requestId) {
    REQUEST_ID.set(requestId);
    MDC.put("requestId", requestId);
  }

  public static String getRequestId() {
    return REQUEST_ID.get();
  }

  // 平台管理员用户相关方法
  public static void setPlatformAdminUserContext(PlatformAdminUser platformAdminUser) {
    if (platformAdminUser == null) {
      throw new IllegalArgumentException("Platform admin user cannot be null");
    }
  }

  public static PlatformAdminUser getPlatformAdminUser() {
    PlatformAdminUser platformAdminUser = PLATFORM_ADMIN_USER_CONTEXT.get();
    if (platformAdminUser == null) {
      throw new IllegalStateException("Platform admin user context not set");
    }
    return platformAdminUser;
  }

  public static boolean isPlatformAdminUser() {
    return PLATFORM_ADMIN_USER_CONTEXT.get() != null;
  }

  public static void setPlatformAdminUserContext(
      Long userId, String firstName, String lastName, String email) {
    if (userId == null) {
      throw new IllegalArgumentException("Platform admin user id cannot be null");
    }
    if (firstName == null) {
      throw new IllegalArgumentException("Platform admin user first name cannot be null");
    }
    if (lastName == null) {
      throw new IllegalArgumentException("Platform admin user last name cannot be null");
    }
    if (email == null) {
      throw new IllegalArgumentException("Platform admin user email cannot be null");
    }

    PLATFORM_ADMIN_USER_CONTEXT.set(new PlatformAdminUser(userId, firstName, lastName, email));
  }

  public static BusinessPartnerUser getBusinessPartnerUser() {
    BusinessPartnerUser businessPartnerUser = BUSINESS_PARTNER_USER_CONTEXT.get();
    if (businessPartnerUser == null) {
      throw new IllegalStateException("Business partner user context not set");
    }
    return businessPartnerUser;
  }

  public static void setBusinessPartnerUserContext(BusinessPartnerUser businessPartnerUser) {
    if (businessPartnerUser == null) {
      throw new IllegalArgumentException("Business partner user cannot be null");
    }
    BUSINESS_PARTNER_USER_CONTEXT.set(businessPartnerUser);
    setAccessType(AccessType.PARTNER_USER);
  }

  // 清理上下文
  public static void clearAll() {
    try {
      log.debug("Clearing all context for request: {}", getRequestId());
      CLIENT_USER_CONTEXT.remove();
      TENANT_USER_CONTEXT.remove();
      SESSION_CONTEXT.remove();
      ORGANIZATION_CONTEXT.remove();
      REQUEST_ID.remove();
      TENANT_FILTER_ENABLED.remove();
      INTERNAL_CALL.remove();
      ACCESS_TYPE_CONTEXT.remove();
      PLATFORM_ADMIN_USER_CONTEXT.remove();
    } finally {
      MDC.clear();
    }
  }

  public static <T> T executeWithoutTenantFilter(Supplier<T> supplier) {
    try {
      AuthContextUtil.disableTenantFilter();
      return supplier.get();
    } finally {
      AuthContextUtil.enableTenantFilter();
    }
  }

  public static Long getTenantId() {
    return switch (getAccessType()) {
      case TENANT -> AuthContextUtil.getTenantUser().getTenantId();
      case PARTNER_USER -> AuthContextUtil.getBusinessPartnerUser().getTenantId();
      default -> null;
    };
  }

  public static Long getPartnerId() {
    return switch (getAccessType()) {
      case PARTNER_USER -> AuthContextUtil.getBusinessPartnerUser().getPartnerId();
      default -> null;
    };
  }

  public static boolean isTenantRequest() {
    return switch (getAccessType()) {
      case TENANT -> true;
      default -> false;
    };
  }

  public static boolean isPartnerRequest() {
    return switch (getAccessType()) {
      case PARTNER_USER -> true;
      default -> false;
    };
  }
}
