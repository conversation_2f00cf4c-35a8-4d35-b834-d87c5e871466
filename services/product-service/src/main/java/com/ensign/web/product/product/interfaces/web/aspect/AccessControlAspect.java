package com.ensign.web.product.product.interfaces.web.aspect;

import com.ensign.web.product.product.interfaces.web.annotation.RequireAccess;
import com.ensign.web.product.product.domain.enums.ProductResultCode;
import com.ensign.starter.common.exception.BizException;
import com.ensign.starter.util.security.AuthContextUtil;
import com.ensign.starter.util.security.AccessType;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * 访问控制切面
 * 处理@RequireAccess注解的权限验证
 * 
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class AccessControlAspect {

    /**
     * 在方法执行前进行权限验证
     */
    @Before("@annotation(requireAccess)")
    public void validateAccess(JoinPoint joinPoint, RequireAccess requireAccess) {
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        
        log.debug("开始权限验证，类：{}，方法：{}", className, methodName);
        
        // 获取当前访问类型
        AccessType currentAccessType = AuthContextUtil.getAccessType();
        if (currentAccessType == null) {
            log.warn("访问类型为空，拒绝访问，类：{}，方法：{}", className, methodName);
            throw new BizException(ProductResultCode.UNAUTHORIZED_ACCESS);
        }
        
        // 检查访问类型是否被允许
        AccessType[] allowedTypes = requireAccess.value();
        boolean hasPermission = Arrays.asList(allowedTypes).contains(currentAccessType);
        
        if (!hasPermission) {
            log.warn("访问类型不匹配，当前类型：{}，允许类型：{}，类：{}，方法：{}", 
                    currentAccessType, Arrays.toString(allowedTypes), className, methodName);
            throw new BizException(ProductResultCode.UNAUTHORIZED_ACCESS);
        }
        
        // 如果需要验证业务伙伴关系
        if (requireAccess.validatePartnerRelation() && currentAccessType == AccessType.TENANT) {
            validateTenantPartnerRelation(joinPoint);
        }
        
        log.debug("权限验证通过，访问类型：{}，类：{}，方法：{}", currentAccessType, className, methodName);
    }
    
    /**
     * 验证租户与业务伙伴的关系
     */
    private void validateTenantPartnerRelation(JoinPoint joinPoint) {
        // 从方法参数中获取partnerId
        Object[] args = joinPoint.getArgs();
        Long partnerId = null;
        
        // 查找Long类型的partnerId参数（通常是第一个Long参数）
        for (Object arg : args) {
            if (arg instanceof Long) {
                partnerId = (Long) arg;
                break;
            }
        }
        
        if (partnerId == null) {
            log.warn("无法从方法参数中获取业务伙伴ID");
            throw new BizException(ProductResultCode.UNAUTHORIZED_ACCESS);
        }
        
        Long tenantId = AuthContextUtil.getTenantId();
        
        // TODO: 调用业务伙伴服务验证租户与业务伙伴的关系
        // 这里应该实现具体的业务逻辑验证
        // boolean hasPermission = businessPartnerService.validateTenantPartnerRelation(tenantId, partnerId);
        // if (!hasPermission) {
        //     log.warn("租户{}无权限管理业务伙伴{}", tenantId, partnerId);
        //     throw new BizException(ProductResultCode.UNAUTHORIZED_ACCESS);
        // }
        
        log.debug("租户业务伙伴关系验证通过，租户ID：{}，业务伙伴ID：{}", tenantId, partnerId);
    }
}
