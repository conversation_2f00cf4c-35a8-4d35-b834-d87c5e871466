# Product Service 业务流程设计详解

## 📋 流程概述

本文档详细介绍Product Service中三种商品类型的完整业务流程，包括创建、查询、更新、删除操作的设计原理和实现细节。重点展示"SKU为王"理念在实际业务流程中的体现。

## 🎯 核心业务流程设计原则

### 1. SKU中心化操作
- **创建流程**：先创建Product载体，再生成SKU业务实体
- **查询流程**：优先从SKU维度查询，Product仅提供组织信息
- **更新流程**：直接操作SKU业务属性，Product保持稳定
- **删除流程**：以SKU为单位进行软删除，级联处理聚合关系

### 2. 类型隔离处理
- **单规格商品**：1个Product + 1个ProductSku的简化流程
- **多规格商品**：1个Product + N个ProductSku的批量处理流程
- **组合商品**：1个Product + 1个ProductSku + N个ProductSkuCombo的复杂关系流程

### 3. 业务验证分层
- **参数验证**：接口层的基础参数格式验证
- **业务验证**：应用层的业务规则验证
- **领域验证**：领域层的复杂业务逻辑验证
- **数据验证**：基础设施层的数据完整性验证

## 🚀 商品创建业务流程

### Type 1: 单规格商品创建流程

#### 流程图
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as ProductController
    participant AppService as ProductCommandService
    participant DomainService as ProductDomainService
    participant Repository as ProductRepository
    participant Database as 数据库

    Client->>Controller: POST /api/products/single-spec
    Note over Client,Controller: CreateSingleSpecProductCmd
    
    Controller->>Controller: 参数验证 (@Valid)
    Controller->>AppService: createSingleSpecProduct(cmd)
    
    AppService->>AppService: 命令业务验证
    AppService->>AppService: 获取租户上下文
    Note over AppService: tenantId = AuthContextUtil.getTenantId()
    
    AppService->>DomainService: createSingleSpecProduct(...)
    
    DomainService->>Repository: existsByProductCode(productCode)
    Repository->>Database: SELECT COUNT(*) FROM product WHERE product_code = ?
    Database-->>Repository: 返回结果
    Repository-->>DomainService: false (不存在)
    
    DomainService->>Repository: existsBySkuCode(skuCode)  
    Repository->>Database: SELECT COUNT(*) FROM product_sku WHERE sku_code = ?
    Database-->>Repository: 返回结果
    Repository-->>DomainService: false (不存在)
    
    DomainService->>DomainService: buildSingleSpecProduct()
    Note over DomainService: 构建Product聚合<br/>productCode = skuCode
    
    DomainService->>DomainService: product.isValid()
    Note over DomainService: 验证聚合完整性
    
    DomainService->>Repository: save(product)
    Repository->>Database: INSERT INTO product (...)
    Repository->>Database: INSERT INTO product_sku (...)
    Database-->>Repository: 返回保存结果
    Repository-->>DomainService: 返回聚合
    
    DomainService-->>AppService: 返回创建的Product
    
    AppService->>DomainService: updateSingleSpecSkuDetails(product, skuUpdater)
    Note over AppService,DomainService: 更新SKU详细业务属性<br/>价格、库存、图片等
    
    AppService-->>Controller: 返回 productId
    Controller-->>Client: ApiResponse<Long>
```



## 🎯 总结

Product Service的业务流程设计完美体现了"SKU为王"的核心理念：

### 流程优势
1. **类型隔离**：三种商品类型有独立的创建和处理流程
2. **性能优化**：批量验证、单表查询、缓存策略
3. **业务清晰**：每个流程都有明确的验证和处理逻辑
4. **扩展性强**：新增商品类型或业务规则易于扩展

### 设计特色
1. **SKU中心化**：所有查询和更新操作都优先考虑SKU
2. **聚合完整性**：通过聚合根保证数据一致性
3. **分层验证**：从参数到业务到数据的多层验证机制
4. **事务控制**：领域服务级别的事务边界管理

这种业务流程设计为复杂的商品管理场景提供了高效、稳定、可维护的解决方案。