package com.ensign.web.product.product.interfaces.web.annotation;

import com.ensign.starter.util.security.AccessType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 访问权限验证注解
 * 用于标记需要特定访问类型的方法
 * 
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireAccess {
    
    /**
     * 允许的访问类型
     */
    AccessType[] value();
    
    /**
     * 是否需要验证业务伙伴关系
     * 当租户访问业务伙伴资源时使用
     */
    boolean validatePartnerRelation() default false;
    
    /**
     * 错误消息
     */
    String message() default "访问权限不足";
}
