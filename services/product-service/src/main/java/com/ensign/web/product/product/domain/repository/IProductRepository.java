package com.ensign.web.product.product.domain.repository;

import java.util.Set;

import com.ensign.starter.web.servlet.response.PageResponse;
import com.ensign.web.product.product.application.pojo.query.ProductPageQuery;
import com.ensign.web.product.product.domain.model.Product;

/**
 * 商品聚合仓储接口
 * 负责Product聚合的完整持久化操作
 * 遵循DDD原则：一个聚合一个仓储
 * 
 * <AUTHOR>
 */
public interface IProductRepository {
    
    // ===============================
    // 聚合持久化操作
    // ===============================
    
    /**
     * 保存商品聚合（包含SKU和组合关系）
     */
    Product save(Product product);
    
    // ===============================
    // 查询操作
    // ===============================
    
    /**
     * 根据ID查询商品（不包含SKU）
     */
    Product findById(Long productId);
    
    /**
     * 根据ID查询商品（包含SKU）
     */
    Product findByIdWithSkus(Long productId);
    
    /**
     * 根据商品编码查询
     */
    Product findByProductCode(String productCode, Long tenantId, Long partnerId);
    
    /**
     * 根据商品编码查询（包含SKU）
     */
    Product findByProductCodeWithSkus(String productCode, Long tenantId, Long partnerId);
    
    /**
     * 分页查询商品
     */
    PageResponse<Product> pageProducts(ProductPageQuery query);
    
    // ===============================
    // 存在性检查
    // ===============================
    
    /**
     * 检查商品编码是否存在（全局唯一）
     */
    boolean existsByProductCode(String productCode);
    
    /**
     * 检查商品编码是否在租户内存在
     */
    boolean existsByProductCodeInTenant(String productCode, Long tenantId, Long partnerId);
    
    /**
     * 检查SKU编码是否存在（全局唯一）
     */
    boolean existsBySkuCode(String skuCode);
    
    /**
     * 检查SKU编码是否在租户内存在
     */
    boolean existsBySkuCodeInTenant(String skuCode, Long tenantId, Long partnerId);
    
    /**
     * 批量查找已存在的SKU编码
     */
    Set<String> findExistingSkuCodes(Set<String> skuCodes);
    
    /**
     * 批量验证SKU ID是否存在（用于组合商品成分验证）
     * 
     * @param skuIds SKU ID集合
     * @return 已存在的SKU ID集合
     */
    Set<Long> findExistingSkuIds(Set<Long> skuIds);
    
    /**
     * 批量验证SKU ID是否存在且属于当前租户（用于组合商品成分验证）
     * 
     * @param skuIds SKU ID集合
     * @param tenantId 租户ID
     * @param partnerId 业务伙伴ID
     * @return 已存在且属于当前租户的SKU ID集合
     */
    Set<Long> findExistingSkuIdsByTenant(Set<Long> skuIds, Long tenantId, Long partnerId);
}