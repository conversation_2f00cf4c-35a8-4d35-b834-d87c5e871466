package com.ensign.web.product;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * @Program: ensign-platform @Author: <PERSON><PERSON><PERSON>
 */
@SpringBootApplication(scanBasePackages = "com.ensign")
@EnableDiscoveryClient
@MapperScan("com.ensign.web.product.**.infrastructure.persistence.mapper")
public class ProductServiceApplication {
  public static void main(String[] args) {
    org.springframework.boot.SpringApplication.run(ProductServiceApplication.class, args);
  }
}
