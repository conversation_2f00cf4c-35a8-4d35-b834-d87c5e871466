package com.ensign.web.product.product.application.pojo.cmd;

import java.math.BigDecimal;

import com.ensign.starter.common.enums.product.ProductTypeEnum;
import com.ensign.starter.common.validation.ValidEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 创建单规格商品命令
 * 用于单规格商品的创建，Product编码 = SKU编码
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "创建单规格商品命令")
public class CreateSingleSpecProductCmd {
    
    /**
     * 商品编码（同时也是SKU编码）
     */
    @NotBlank(message = "商品编码不能为空")
    @Size(min = 2, max = 50, message = "商品编码长度必须在2-50个字符之间")
    @Pattern(regexp = "^[A-Z0-9_-]+$", message = "商品编码只能包含大写字母、数字、下划线和中划线")
    @Schema(description = "商品编码（单规格商品的商品编码=SKU编码）", example = "IPHONE14_RED_128G")
    private String productCode;
    
    /**
     * 商品类型（必须为单规格）
     */
    @NotNull(message = "商品类型不能为空")
    @ValidEnum(enumClass = ProductTypeEnum.class, message = "商品类型无效")
    @Schema(description = "商品类型", example = "1", allowableValues = {"1"})
    private Integer productType = ProductTypeEnum.SINGLE_SPEC.getCode();
    
    /**
     * SKU名称
     */
    @NotBlank(message = "SKU名称不能为空")
    @Size(min = 1, max = 200, message = "SKU名称长度必须在1-200个字符之间")
    @Schema(description = "SKU名称", example = "iPhone 14 红色 128G")
    private String skuName;
    
    /**
     * 分类ID
     */
    @Schema(description = "商品分类ID", example = "1001")
    private Long categoryId;
    
    /**
     * 品牌ID
     */
    @Schema(description = "品牌ID", example = "2001")
    private Long brandId;
    
    /**
     * 分类名称（Excel导入支持）
     */
    @Size(max = 100, message = "分类名称长度不能超过100个字符")
    @Schema(description = "分类名称（用于Excel导入，系统会自动匹配分类ID）", example = "智能手机")
    private String categoryName;
    
    /**
     * 品牌名称（Excel导入支持）
     */
    @Size(max = 100, message = "品牌名称长度不能超过100个字符")
    @Schema(description = "品牌名称（用于Excel导入，系统会自动匹配品牌ID）", example = "苹果")
    private String brandName;
    
    // ========== SKU业务字段 ==========
    
    /**
     * 销售价格
     */
    @DecimalMin(value = "0.00", message = "销售价格不能为负数")
    @Digits(integer = 10, fraction = 2, message = "销售价格格式不正确")
    @Schema(description = "销售价格", example = "6999.00")
    private BigDecimal price;

    /**
     * 成本价格
     */
    @DecimalMin(value = "0.00", message = "成本价格不能为负数")
    @Digits(integer = 10, fraction = 2, message = "成本价格格式不正确")
    @Schema(description = "成本价格", example = "5500.00")
    private BigDecimal cost;

    /**
     * 市场价格
     */
    @DecimalMin(value = "0.00", message = "市场价格不能为负数")
    @Digits(integer = 10, fraction = 2, message = "市场价格格式不正确")
    @Schema(description = "市场价格", example = "7999.00")
    private BigDecimal marketPrice;

    /**
     * 价格货币单位
     */
    @Size(max = 10, message = "货币单位长度不能超过10个字符")
    @Schema(description = "价格货币单位", example = "CNY", allowableValues = {"CNY", "USD", "EUR", "JPY"})
    private String currency = "CNY";
    
    /**
     * 初始库存
     */
    @Min(value = 0, message = "初始库存不能为负数")
    @Schema(description = "初始库存数量", example = "100")
    private Integer initialStock = 0;
    
    /**
     * 安全库存
     */
    @Min(value = 0, message = "安全库存不能为负数")
    @Schema(description = "安全库存数量", example = "10")
    private Integer safetyStock = 0;
    
    // ========== 物理属性 ==========

    /**
     * 净重（数值）
     */
    @DecimalMin(value = "0.0000", message = "净重不能为负数")
    @Digits(integer = 10, fraction = 4, message = "净重格式不正确")
    @Schema(description = "净重数值", example = "0.1750")
    private BigDecimal netWeight;

    /**
     * 重量单位
     */
    @Size(max = 10, message = "重量单位长度不能超过10个字符")
    @Schema(description = "重量单位", example = "kg", allowableValues = {"g", "kg", "lb"})
    private String weightUnit = "kg";

    /**
     * 毛重（数值）
     */
    @DecimalMin(value = "0.0000", message = "毛重不能为负数")
    @Digits(integer = 10, fraction = 4, message = "毛重格式不正确")
    @Schema(description = "毛重数值", example = "0.2000")
    private BigDecimal grossWeight;
    
    /**
     * 长度（数值）
     */
    @DecimalMin(value = "0.000", message = "长度不能为负数")
    @Digits(integer = 10, fraction = 3, message = "长度格式不正确")
    @Schema(description = "长度数值", example = "14.700")
    private BigDecimal length;

    /**
     * 宽度（数值）
     */
    @DecimalMin(value = "0.000", message = "宽度不能为负数")
    @Digits(integer = 10, fraction = 3, message = "宽度格式不正确")
    @Schema(description = "宽度数值", example = "7.150")
    private BigDecimal width;

    /**
     * 高度（数值）
     */
    @DecimalMin(value = "0.000", message = "高度不能为负数")
    @Digits(integer = 10, fraction = 3, message = "高度格式不正确")
    @Schema(description = "高度数值", example = "0.780")
    private BigDecimal height;

    /**
     * 尺寸单位
     */
    @Size(max = 10, message = "尺寸单位长度不能超过10个字符")
    @Schema(description = "尺寸单位", example = "cm", allowableValues = {"mm", "cm", "in"})
    private String dimensionUnit = "cm";
    
    // ========== 展示信息 ==========
    
    /**
     * 主图URL
     */
    @Size(max = 255, message = "主图URL长度不能超过255个字符")
    @Schema(description = "商品主图URL", example = "https://example.com/images/iphone14_red_main.jpg")
    private String mainImage;
    
    /**
     * 商品视频链接
     */
    @Size(max = 500, message = "视频链接长度不能超过500个字符")
    @Schema(description = "商品视频链接", example = "https://example.com/videos/iphone14_intro.mp4")
    private String videoLink;
    
    // ========== 营销属性 ==========
    
    /**
     * 是否推荐商品
     */
    @Schema(description = "是否推荐商品", example = "false")
    private Boolean featured = false;
    
    /**
     * 是否新品
     */
    @Schema(description = "是否新品", example = "true")
    private Boolean newArrival = false;
    
    // ========== 配送信息 ==========
    
    /**
     * 运费模板ID
     */
    @Schema(description = "运费模板ID", example = "3001")
    private Long shippingTemplateId;
    
    /**
     * 包邮门槛
     */
    @DecimalMin(value = "0.00", message = "包邮门槛不能为负数")
    @Digits(integer = 10, fraction = 2, message = "包邮门槛格式不正确")
    @Schema(description = "包邮门槛金额", example = "99.00")
    private BigDecimal freeShippingThreshold;
    
    // ===============================
    // 业务验证方法
    // ===============================
    
    /**
     * 验证商品类型是否为单规格
     */
    public boolean isValidProductType() {
        return ProductTypeEnum.SINGLE_SPEC.getCode().equals(productType);
    }
    
    /**
     * 验证价格逻辑
     */
    public boolean isValidPricing() {
        if (price == null) {
            return true; // 价格可以为空，后续设置
        }
        
        // 如果设置了成本价，销售价应该大于等于成本价
        if (cost != null && price.compareTo(cost) < 0) {
            return false;
        }
        
        // 如果设置了市场价，市场价应该大于等于销售价
        if (marketPrice != null && marketPrice.compareTo(price) < 0) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证库存逻辑
     */
    public boolean isValidStock() {
        if (safetyStock != null && initialStock != null) {
            // 初始库存应该大于等于安全库存
            return initialStock >= safetyStock;
        }
        return true;
    }
    
    /**
     * 验证物理属性逻辑
     */
    public boolean isValidPhysical() {
        // 如果设置了毛重，毛重应该大于等于净重
        if (netWeight != null && grossWeight != null) {
            return grossWeight.compareTo(netWeight) >= 0;
        }
        return true;
    }
    
    /**
     * 验证命令完整性
     */
    public boolean isValid() {
        return isValidProductType() && 
               isValidPricing() && 
               isValidStock() && 
               isValidPhysical();
    }
}