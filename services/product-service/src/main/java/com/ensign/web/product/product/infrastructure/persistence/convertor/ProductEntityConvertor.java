package com.ensign.web.product.product.infrastructure.persistence.convertor;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import com.ensign.web.product.product.domain.model.Product;
import com.ensign.web.product.product.domain.model.ProductSku;
import com.ensign.web.product.product.domain.model.ProductSkuCombo;
import com.ensign.web.product.product.infrastructure.persistence.entity.ProductEntity;
import com.ensign.web.product.product.infrastructure.persistence.entity.ProductSkuComboEntity;
import com.ensign.web.product.product.infrastructure.persistence.entity.ProductSkuEntity;

/**
 * 商品基础设施层转换器
 * ：使用spring componentModel，标准命名（toDomain/toEntity）
 * 负责领域对象 ↔ Entity对象的转换
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProductEntityConvertor {

    // ===============================
    // Product Entity ↔ Domain 转换
    // ===============================

    /**
     * 聚合实体包装类
     * 用于Repository层的批量操作
     */
    class ProductAggregateEntities {
        private final ProductEntity productEntity;
        private final List<ProductSkuEntity> skuEntities;
        private final List<ProductSkuComboEntity> comboEntities;

        public ProductAggregateEntities(ProductEntity productEntity,
                                      List<ProductSkuEntity> skuEntities,
                                      List<ProductSkuComboEntity> comboEntities) {
            this.productEntity = productEntity;
            this.skuEntities = skuEntities;
            this.comboEntities = comboEntities;
        }

        public ProductEntity getProductEntity() {
            return productEntity;
        }

        public List<ProductSkuEntity> getSkuEntities() {
            return skuEntities;
        }

        public List<ProductSkuComboEntity> getComboEntities() {
            return comboEntities;
        }

        public boolean hasSkus() {
            return skuEntities != null && !skuEntities.isEmpty();
        }

        public boolean hasComboRelations() {
            return comboEntities != null && !comboEntities.isEmpty();
        }
    }

    /**
     * Product实体转领域对象
     */
    @Mapping(target = "skus", ignore = true) // 在Repository层单独处理
    @Mapping(target = "comboRelations", ignore = true) // 在Repository层单独处理
    Product toDomain(ProductEntity entity);

    /**
     * Product领域对象转实体
     */
    ProductEntity toEntity(Product domain);

    /**
     * Product实体列表转领域对象列表
     */
    List<Product> toDomainList(List<ProductEntity> entities);

    // ===============================
    // ProductSku Entity ↔ Domain 转换
    // ===============================

    /**
     * Product领域对象列表转实体列表
     */
    List<ProductEntity> toEntityList(List<Product> domains);

    /**
     * ProductSku实体转领域对象
     */
    @Mapping(target = "expiryManagementEnabled", expression = "java(integerToBoolean(entity.getExpiryManagementEnabled()))")
    ProductSku toSkuDomain(ProductSkuEntity entity);

    /**
     * ProductSku领域对象转实体
     */
    @Mapping(target = "expiryManagementEnabled", expression = "java(booleanToInteger(domain.getExpiryManagementEnabled()))")
    ProductSkuEntity toSkuEntity(ProductSku domain);

    /**
     * ProductSku实体列表转领域对象列表
     */
    List<ProductSku> toSkuDomainList(List<ProductSkuEntity> entities);

    // ===============================
    // ProductSkuCombo Entity ↔ Domain 转换
    // ===============================

    /**
     * ProductSku领域对象列表转实体列表
     */
    List<ProductSkuEntity> toSkuEntityList(List<ProductSku> domains);

    /**
     * ProductSkuCombo实体转领域对象
     */
    ProductSkuCombo toComboRelationDomain(ProductSkuComboEntity entity);

    /**
     * ProductSkuCombo领域对象转实体
     */
    ProductSkuComboEntity toComboRelationEntity(ProductSkuCombo domain);

    /**
     * ProductSkuCombo实体列表转领域对象列表
     */
    List<ProductSkuCombo> toComboRelationDomainList(List<ProductSkuComboEntity> entities);

    // ===============================
    // 聚合完整转换（包含关联对象）
    // ===============================

    /**
     * ProductSkuCombo领域对象列表转实体列表
     */
    List<ProductSkuComboEntity> toComboRelationEntityList(List<ProductSkuCombo> domains);

    /**
     * 完整的Product聚合转换（包含SKU和组合关系）
     * 用于Repository层的聚合加载
     */
    default Product toFullDomain(ProductEntity productEntity,
                                List<ProductSkuEntity> skuEntities,
                                List<ProductSkuComboEntity> comboEntities) {
        if (productEntity == null) {
            return null;
        }

        Product product = toDomain(productEntity);

        // 转换SKU列表
        if (skuEntities != null && !skuEntities.isEmpty()) {
            List<ProductSku> skus = toSkuDomainList(skuEntities);
            product.addSkus(skus);
        }

        // 转换组合关系列表
        if (comboEntities != null && !comboEntities.isEmpty()) {
            List<ProductSkuCombo> comboRelations = toComboRelationDomainList(comboEntities);
            product.addComboRelations(comboRelations);
        }

        return product;
    }

    /**
     * 完整的Product聚合转换（分解为多个Entity）
     * 用于Repository层的聚合保存
     */
    default ProductAggregateEntities toFullEntities(Product product) {
        if (product == null) {
            return null;
        }

        ProductEntity productEntity = toEntity(product);
        List<ProductSkuEntity> skuEntities = null;
        List<ProductSkuComboEntity> comboEntities = null;

        // 转换SKU列表
        if (product.hasSkus()) {
            skuEntities = toSkuEntityList(product.getSkus());
            // 设置外键关系
            for (ProductSkuEntity skuEntity : skuEntities) {
                skuEntity.setProductId(productEntity.getId());
            }
        }

        // 转换组合关系列表
        if (product.hasComboRelations()) {
            comboEntities = toComboRelationEntityList(product.getComboRelations());
        }

        return new ProductAggregateEntities(productEntity, skuEntities, comboEntities);
    }
}
