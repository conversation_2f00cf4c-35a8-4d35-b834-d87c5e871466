package com.ensign.web.product.product.infrastructure.persistence.repository;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ensign.starter.web.servlet.response.PageResponse;
import com.ensign.web.product.product.application.pojo.query.ProductPageQuery;
import com.ensign.web.product.product.domain.model.Product;
import com.ensign.web.product.product.domain.model.ProductSku;
import com.ensign.web.product.product.domain.model.ProductSkuCombo;
import com.ensign.web.product.product.domain.repository.IProductRepository;
import com.ensign.web.product.product.infrastructure.persistence.convertor.ProductEntityConvertor;
import com.ensign.web.product.product.infrastructure.persistence.entity.ProductEntity;
import com.ensign.web.product.product.infrastructure.persistence.entity.ProductSkuComboEntity;
import com.ensign.web.product.product.infrastructure.persistence.entity.ProductSkuEntity;
import com.ensign.web.product.product.infrastructure.persistence.mapper.ProductMapper;
import com.ensign.web.product.product.infrastructure.persistence.mapper.ProductSkuComboMapper;
import com.ensign.web.product.product.infrastructure.persistence.mapper.ProductSkuMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 商品聚合仓储实现
 * 遵循DDD原则：一个聚合一个仓储
 * 负责Product聚合的完整持久化操作
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ProductRepositoryImpl implements IProductRepository {

    private final ProductMapper productMapper;
    private final ProductSkuMapper productSkuMapper;
    private final ProductSkuComboMapper productSkuComboMapper;
    private final ProductEntityConvertor productEntityConvertor;

    // ===============================
    // 商品聚合保存
    // ===============================

    /**
     * 保存商品聚合（包含SKU和组合关系）
     */
    @Override
    @Transactional
    public Product save(Product product) {
        log.debug("保存商品聚合，商品编码：{}，SKU数量：{}",
                product.getProductCode(), product.getSkuCount());

        // 1. 保存商品主表
        ProductEntity productEntity = productEntityConvertor.toEntity(product);
        if (product.getId() == null) {
            // 新增
            productMapper.insert(productEntity);
            product.setId(productEntity.getId());
        } else {
            // 更新
            productMapper.updateById(productEntity);
        }

        // 2. 保存SKU列表
        if (product.hasSkus()) {
            saveSkuList(product);
        }

        // 3. 保存组合关系（如果是组合商品）
        if (product.isComboProduct() && product.hasComboRelations()) {
            saveComboRelations(product);
        }

        log.debug("商品聚合保存成功，商品ID：{}", product.getId());
        return product;
    }

    /**
     * 根据ID查询商品（不包含SKU）
     */
    @Override
    public Product findById(Long productId) {
        ProductEntity entity = productMapper.selectById(productId);
        if (entity == null) {
            return null;
        }
        return productEntityConvertor.toDomain(entity);
    }

    /**
     * 根据ID查询商品（包含SKU）
     */
    @Override
    public Product findByIdWithSkus(Long productId) {
        Product product = findById(productId);
        if (product == null) {
            return null;
        }

        // 查询SKU列表
        loadSkusForProduct(product);

        // 如果是组合商品，查询组合关系
        if (product.isComboProduct()) {
            loadComboRelationsForProduct(product);
        }

        return product;
    }

    // ===============================
    // 商品查询
    // ===============================

    /**
     * 根据商品编码查询
     */
    @Override
    public Product findByProductCode(String productCode, Long tenantId, Long partnerId) {
        LambdaQueryWrapper<ProductEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductEntity::getProductCode, productCode)
               .eq(ProductEntity::getTenantId, tenantId)
               .eq(ProductEntity::getPartnerId, partnerId);

        ProductEntity entity = productMapper.selectOne(wrapper);
        if (entity == null) {
            return null;
        }
        return productEntityConvertor.toDomain(entity);
    }

    /**
     * 根据商品编码查询（包含SKU）
     */
    @Override
    public Product findByProductCodeWithSkus(String productCode, Long tenantId, Long partnerId) {
        Product product = findByProductCode(productCode, tenantId, partnerId);
        if (product == null) {
            return null;
        }

        // 查询SKU列表
        loadSkusForProduct(product);

        // 如果是组合商品，查询组合关系
        if (product.isComboProduct()) {
            loadComboRelationsForProduct(product);
        }

        return product;
    }

    /**
     * 分页查询商品
     */
    @Override
    public PageResponse<Product> pageProducts(ProductPageQuery query) {
        // 构建查询条件
        LambdaQueryWrapper<ProductEntity> wrapper = buildProductQueryWrapper(query);

        // 执行分页查询
        Page<ProductEntity> entityPage = new Page<>(query.getPageNum(), query.getPageSize());
        Page<ProductEntity> resultPage = productMapper.selectPage(entityPage, wrapper);

        // 转换为领域对象
        List<Product> products = resultPage.getRecords().stream()
                .map(productEntityConvertor::toDomain)
                .collect(Collectors.toList());

        // 使用PageResponse.of()创建分页响应
        return PageResponse.of(
                resultPage.getCurrent(),
                resultPage.getSize(),
                resultPage.getTotal(),
                products
        );
    }

    /**
     * 检查商品编码是否存在（全局唯一）
     */
    @Override
    public boolean existsByProductCode(String productCode) {
        LambdaQueryWrapper<ProductEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductEntity::getProductCode, productCode)
               .eq(ProductEntity::getDeleted, false);

        return productMapper.selectCount(wrapper) > 0;
    }

    /**
     * 检查商品编码是否在租户内存在
     */
    @Override
    public boolean existsByProductCodeInTenant(String productCode, Long tenantId, Long partnerId) {
        LambdaQueryWrapper<ProductEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductEntity::getProductCode, productCode)
               .eq(ProductEntity::getTenantId, tenantId)
               .eq(ProductEntity::getPartnerId, partnerId)
               .eq(ProductEntity::getDeleted, false);

        return productMapper.selectCount(wrapper) > 0;
    }

    /**
     * 检查SKU编码是否存在（全局唯一）
     */
    @Override
    public boolean existsBySkuCode(String skuCode) {
        LambdaQueryWrapper<ProductSkuEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductSkuEntity::getSkuCode, skuCode)
               .eq(ProductSkuEntity::getDeleted, false);

        return productSkuMapper.selectCount(wrapper) > 0;
    }

    // ===============================
    // 存在性检查
    // ===============================

    /**
     * 检查SKU编码是否在租户内存在
     */
    @Override
    public boolean existsBySkuCodeInTenant(String skuCode, Long tenantId, Long partnerId) {
        LambdaQueryWrapper<ProductSkuEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductSkuEntity::getSkuCode, skuCode)
               .eq(ProductSkuEntity::getTenantId, tenantId)
               .eq(ProductSkuEntity::getPartnerId, partnerId)
               .eq(ProductSkuEntity::getDeleted, false);

        return productSkuMapper.selectCount(wrapper) > 0;
    }

    /**
     * 批量查找已存在的SKU编码
     */
    @Override
    public Set<String> findExistingSkuCodes(Set<String> skuCodes) {
        if (skuCodes == null || skuCodes.isEmpty()) {
            return Set.of();
        }

        LambdaQueryWrapper<ProductSkuEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProductSkuEntity::getSkuCode, skuCodes)
               .eq(ProductSkuEntity::getDeleted, false)
               .select(ProductSkuEntity::getSkuCode);

        List<ProductSkuEntity> entities = productSkuMapper.selectList(wrapper);
        return entities.stream()
                .map(ProductSkuEntity::getSkuCode)
                .collect(Collectors.toSet());
    }

    /**
     * 批量验证SKU ID是否存在
     */
    @Override
    public Set<Long> findExistingSkuIds(Set<Long> skuIds) {
        if (skuIds == null || skuIds.isEmpty()) {
            return Set.of();
        }

        LambdaQueryWrapper<ProductSkuEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProductSkuEntity::getId, skuIds)
               .eq(ProductSkuEntity::getDeleted, false)
               .select(ProductSkuEntity::getId);

        List<ProductSkuEntity> entities = productSkuMapper.selectList(wrapper);
        return entities.stream()
                .map(ProductSkuEntity::getId)
                .collect(Collectors.toSet());
    }

    /**
     * 批量验证SKU ID是否存在且属于当前租户
     */
    @Override
    public Set<Long> findExistingSkuIdsByTenant(Set<Long> skuIds, Long tenantId, Long partnerId) {
        if (skuIds == null || skuIds.isEmpty()) {
            return Set.of();
        }

        LambdaQueryWrapper<ProductSkuEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProductSkuEntity::getId, skuIds)
               .eq(ProductSkuEntity::getTenantId, tenantId)
               .eq(ProductSkuEntity::getPartnerId, partnerId)
               .eq(ProductSkuEntity::getDeleted, false)
               .select(ProductSkuEntity::getId);

        List<ProductSkuEntity> entities = productSkuMapper.selectList(wrapper);
        return entities.stream()
                .map(ProductSkuEntity::getId)
                .collect(Collectors.toSet());
    }

    /**
     * 保存SKU列表
     */
    private void saveSkuList(Product product) {
        for (ProductSku sku : product.getSkus()) {
            ProductSkuEntity skuEntity = productEntityConvertor.toSkuEntity(sku);
            skuEntity.setProductId(product.getId());

            if (sku.getId() == null) {
                // 新增SKU
                productSkuMapper.insert(skuEntity);
                sku.setId(skuEntity.getId());
            } else {
                // 更新SKU
                productSkuMapper.updateById(skuEntity);
            }
        }
    }

    /**
     * 保存组合关系
     */
    private void saveComboRelations(Product product) {
        Long comboSkuId = product.getSkus().getFirst().getId(); // 组合商品只有一个SKU

        for (ProductSkuCombo relation : product.getComboRelations()) {
            ProductSkuComboEntity relationEntity = productEntityConvertor.toComboEntity(relation);
            relationEntity.setComboSkuId(comboSkuId);

            if (relation.getId() == null) {
                // 新增关系
                productSkuComboMapper.insert(relationEntity);
                relation.setId(relationEntity.getId());
            } else {
                // 更新关系
                productSkuComboMapper.updateById(relationEntity);
            }
        }
    }

    /**
     * 构建商品查询条件
     */
    private LambdaQueryWrapper<ProductEntity> buildProductQueryWrapper(ProductPageQuery query) {
        LambdaQueryWrapper<ProductEntity> wrapper = new LambdaQueryWrapper<>();

        // 基础条件
        wrapper.eq(ProductEntity::getTenantId, query.getTenantId())
               .eq(ProductEntity::getPartnerId, query.getPartnerId())
               .eq(ProductEntity::getDeleted, false);

        // 商品编码模糊查询
        if (query.hasProductCodeFilter()) {
            wrapper.like(ProductEntity::getProductCode, query.getProductCode());
        }

        // 商品类型精确查询
        if (query.hasProductTypeFilter()) {
            wrapper.eq(ProductEntity::getProductType, query.getProductType());
        }

        // 分类条件
        if (query.hasCategoryFilter()) {
            if (query.getCategoryId() != null) {
                wrapper.eq(ProductEntity::getCategoryId, query.getCategoryId());
            }
            if (query.getCategoryName() != null && !query.getCategoryName().trim().isEmpty()) {
                wrapper.like(ProductEntity::getCategoryName, query.getCategoryName());
            }
        }

        // 品牌条件
        if (query.hasBrandFilter()) {
            if (query.getBrandId() != null) {
                wrapper.eq(ProductEntity::getBrandId, query.getBrandId());
            }
            if (query.getBrandName() != null && !query.getBrandName().trim().isEmpty()) {
                wrapper.like(ProductEntity::getBrandName, query.getBrandName());
            }
        }

        // 时间范围条件
        if (query.hasTimeRangeFilter()) {
            if (query.getCreatedAtStart() != null && !query.getCreatedAtStart().trim().isEmpty()) {
                wrapper.ge(ProductEntity::getCreatedAt, query.getCreatedAtStart() + " 00:00:00");
            }
            if (query.getCreatedAtEnd() != null && !query.getCreatedAtEnd().trim().isEmpty()) {
                wrapper.le(ProductEntity::getCreatedAt, query.getCreatedAtEnd() + " 23:59:59");
            }
        }

        // 排序：最新创建的在前
        wrapper.orderByDesc(ProductEntity::getCreatedAt);

        return wrapper;
    }

    // ===============================
    // 辅助方法
    // ===============================

    /**
     * 为商品加载SKU列表
     */
    private void loadSkusForProduct(Product product) {
        LambdaQueryWrapper<ProductSkuEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductSkuEntity::getProductId, product.getId())
               .eq(ProductSkuEntity::getDeleted, false)
               .orderByAsc(ProductSkuEntity::getId);

        List<ProductSkuEntity> skuEntities = productSkuMapper.selectList(wrapper);
        List<com.ensign.web.product.product.domain.model.ProductSku> skus = skuEntities.stream()
                .map(productEntityConvertor::toSkuDomain)
                .collect(Collectors.toList());

        product.setSkus(skus);
    }

    /**
     * 为组合商品加载组合关系
     */
    private void loadComboRelationsForProduct(Product product) {
        if (!product.hasSkus()) {
            return;
        }

        Long comboSkuId = product.getSkus().get(0).getId();

        LambdaQueryWrapper<ProductSkuComboEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductSkuComboEntity::getComboSkuId, comboSkuId)
               .eq(ProductSkuComboEntity::getDeleted, false)
               .orderByAsc(ProductSkuComboEntity::getSortOrder, ProductSkuComboEntity::getId);

        List<ProductSkuComboEntity> comboEntities = productSkuComboMapper.selectList(wrapper);
        List<com.ensign.web.product.product.domain.model.ProductSkuCombo> comboRelations = comboEntities.stream()
                .map(productEntityConvertor::toComboDomain)
                .collect(Collectors.toList());

        product.setComboRelations(comboRelations);
    }
}